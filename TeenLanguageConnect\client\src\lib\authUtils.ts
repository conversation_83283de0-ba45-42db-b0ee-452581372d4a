// API configuration for PHP backend
export const API_BASE_URL = '/php-backend/api';

// API utility functions
export async function apiCall(endpoint: string, options: RequestInit = {}) {
  const url = `${API_BASE_URL}${endpoint}`;

  const defaultOptions: RequestInit = {
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  };

  const response = await fetch(url, { ...defaultOptions, ...options });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(`${response.status}: ${errorData.error || response.statusText}`);
  }

  return response.json();
}

export function isUnauthorizedError(error: Error): boolean {
  return /^401: /.test(error.message);
}

// Authentication API calls
export async function loginUser(phoneNumber: string, password: string) {
  return apiCall('/auth/login', {
    method: 'POST',
    body: JSON.stringify({ phoneNumber, password }),
  });
}

export async function registerUser(userData: any) {
  return apiCall('/auth/register', {
    method: 'POST',
    body: JSON.stringify(userData),
  });
}

export async function logoutUser() {
  return apiCall('/auth/logout', {
    method: 'POST',
  });
}

export async function getCurrentUser() {
  return apiCall('/auth/user');
}

// Posts API calls
export async function getFeed(page = 1, limit = 10) {
  return apiCall(`/posts/feed?page=${page}&limit=${limit}`);
}

export async function createPost(postData: any) {
  return apiCall('/posts', {
    method: 'POST',
    body: JSON.stringify(postData),
  });
}

export async function likePost(postId: number) {
  return apiCall(`/posts/${postId}/like`, {
    method: 'POST',
  });
}

// Users API calls
export async function getUserProfile(userId: string) {
  return apiCall(`/users/${userId}`);
}

export async function followUser(userId: string) {
  return apiCall(`/users/${userId}/follow`, {
    method: 'POST',
  });
}

export async function unfollowUser(userId: string) {
  return apiCall(`/users/${userId}/unfollow`, {
    method: 'POST',
  });
}

// Groups API calls
export async function getGroups(page = 1, limit = 20, language?: string, search?: string) {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
  });

  if (language) params.append('language', language);
  if (search) params.append('search', search);

  return apiCall(`/groups?${params.toString()}`);
}

export async function createGroup(groupData: any) {
  return apiCall('/groups', {
    method: 'POST',
    body: JSON.stringify(groupData),
  });
}

export async function getGroup(groupId: number) {
  return apiCall(`/groups/${groupId}`);
}

export async function joinGroup(groupId: number) {
  return apiCall(`/groups/${groupId}/join`, {
    method: 'POST',
  });
}

// Messages API calls
export async function getGroupMessages(groupId: number, page = 1, limit = 50) {
  return apiCall(`/groups/${groupId}/messages?page=${page}&limit=${limit}`);
}

export async function sendMessage(groupId: number, messageData: any) {
  return apiCall(`/groups/${groupId}/messages`, {
    method: 'POST',
    body: JSON.stringify(messageData),
  });
}

// File upload
export async function uploadFile(file: File, type: string = 'general') {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('type', type);

  return apiCall('/upload', {
    method: 'POST',
    headers: {}, // Remove Content-Type to let browser set it for FormData
    body: formData,
  });
}