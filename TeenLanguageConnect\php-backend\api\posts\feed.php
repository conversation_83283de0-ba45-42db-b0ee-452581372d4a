<?php
/**
 * Get User Feed API
 */

try {
    // Only allow GET requests
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $currentUserId = getCurrentUserId();
    $pagination = paginate($_GET['page'] ?? 1, $_GET['limit'] ?? 10);
    
    $db = Database::getInstance();
    
    // Clean expired stories first
    cleanExpiredStories();
    
    // Get posts from followed users and own posts
    $posts = $db->fetchAll("
        SELECT 
            p.*,
            u.username,
            u.first_name,
            u.last_name,
            u.profile_image_url,
            u.country,
            (SELECT COUNT(*) FROM likes l WHERE l.post_id = p.id AND l.user_id = ?) as user_liked
        FROM posts p
        JOIN users u ON p.user_id = u.id
        WHERE (
            p.user_id = ? 
            OR p.user_id IN (
                SELECT following_id FROM follows WHERE follower_id = ?
            )
        )
        AND p.is_story = 0
        AND u.is_blocked = 0
        AND p.user_id NOT IN (
            SELECT blocked_id FROM blocked_users WHERE blocker_id = ?
        )
        ORDER BY p.created_at DESC
        LIMIT ? OFFSET ?
    ", [
        $currentUserId, 
        $currentUserId, 
        $currentUserId, 
        $currentUserId,
        $pagination['limit'], 
        $pagination['offset']
    ]);
    
    // Format posts data
    $formattedPosts = [];
    foreach ($posts as $post) {
        $userData = [
            'id' => $post['user_id'],
            'username' => $post['username'],
            'first_name' => $post['first_name'],
            'last_name' => $post['last_name'],
            'profile_image_url' => $post['profile_image_url'],
            'country' => $post['country']
        ];
        
        $postData = formatPostData($post, $userData);
        $postData['userLiked'] = (bool)$post['user_liked'];
        
        $formattedPosts[] = $postData;
    }
    
    sendJsonResponse([
        'posts' => $formattedPosts,
        'pagination' => [
            'page' => $pagination['page'],
            'limit' => $pagination['limit'],
            'hasMore' => count($formattedPosts) === $pagination['limit']
        ]
    ]);
    
} catch (Exception $e) {
    logError('Get feed error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
