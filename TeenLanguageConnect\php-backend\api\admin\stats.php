<?php
/**
 * Admin Platform Statistics API
 */

try {
    // Only allow GET requests
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require admin authentication
    requireAdmin();
    
    $db = Database::getInstance();
    
    // Get basic statistics
    $stats = $db->fetchOne("
        SELECT 
            (SELECT COUNT(*) FROM users WHERE is_blocked = 0) as total_users,
            (SELECT COUNT(*) FROM users WHERE is_blocked = 1) as blocked_users,
            (SELECT COUNT(*) FROM users WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as new_users_week,
            (SELECT COUNT(*) FROM users WHERE last_active >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as active_users_today,
            (SELECT COUNT(*) FROM posts WHERE is_story = 0) as total_posts,
            (SELECT COUNT(*) FROM posts WHERE is_story = 1 AND story_expires_at > NOW()) as active_stories,
            (SELECT COUNT(*) FROM posts WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as new_posts_week,
            (SELECT COUNT(*) FROM groups WHERE is_active = 1) as active_groups,
            (SELECT COUNT(*) FROM groups WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)) as new_groups_week,
            (SELECT COUNT(*) FROM messages WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as messages_today,
            (SELECT COUNT(*) FROM reports WHERE status = 'pending') as pending_reports,
            (SELECT COUNT(*) FROM likes WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)) as likes_today
    ");
    
    // Get user growth data (last 30 days)
    $userGrowth = $db->fetchAll("
        SELECT 
            DATE(created_at) as date,
            COUNT(*) as new_users
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        GROUP BY DATE(created_at)
        ORDER BY date ASC
    ");
    
    // Get top languages
    $topLanguages = $db->fetchAll("
        SELECT 
            language,
            COUNT(*) as group_count
        FROM groups 
        WHERE is_active = 1 AND language IS NOT NULL
        GROUP BY language
        ORDER BY group_count DESC
        LIMIT 10
    ");
    
    // Get top countries
    $topCountries = $db->fetchAll("
        SELECT 
            country,
            COUNT(*) as user_count
        FROM users 
        WHERE country IS NOT NULL AND is_blocked = 0
        GROUP BY country
        ORDER BY user_count DESC
        LIMIT 10
    ");
    
    // Get recent activity
    $recentActivity = $db->fetchAll("
        SELECT 
            'user_registered' as type,
            CONCAT(first_name, ' ', last_name) as description,
            created_at as timestamp
        FROM users 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        
        UNION ALL
        
        SELECT 
            'post_created' as type,
            CONCAT('New post by ', u.first_name, ' ', u.last_name) as description,
            p.created_at as timestamp
        FROM posts p
        JOIN users u ON p.user_id = u.id
        WHERE p.created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) AND p.is_story = 0
        
        UNION ALL
        
        SELECT 
            'group_created' as type,
            CONCAT('New group: ', name) as description,
            created_at as timestamp
        FROM groups 
        WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        
        ORDER BY timestamp DESC
        LIMIT 20
    ");
    
    // Format response
    $response = [
        'overview' => [
            'totalUsers' => (int)$stats['total_users'],
            'blockedUsers' => (int)$stats['blocked_users'],
            'newUsersThisWeek' => (int)$stats['new_users_week'],
            'activeUsersToday' => (int)$stats['active_users_today'],
            'totalPosts' => (int)$stats['total_posts'],
            'activeStories' => (int)$stats['active_stories'],
            'newPostsThisWeek' => (int)$stats['new_posts_week'],
            'activeGroups' => (int)$stats['active_groups'],
            'newGroupsThisWeek' => (int)$stats['new_groups_week'],
            'messagesToday' => (int)$stats['messages_today'],
            'pendingReports' => (int)$stats['pending_reports'],
            'likesToday' => (int)$stats['likes_today']
        ],
        'userGrowth' => array_map(function($item) {
            return [
                'date' => $item['date'],
                'newUsers' => (int)$item['new_users']
            ];
        }, $userGrowth),
        'topLanguages' => array_map(function($item) {
            return [
                'language' => $item['language'],
                'groupCount' => (int)$item['group_count']
            ];
        }, $topLanguages),
        'topCountries' => array_map(function($item) {
            return [
                'country' => $item['country'],
                'userCount' => (int)$item['user_count']
            ];
        }, $topCountries),
        'recentActivity' => array_map(function($item) {
            return [
                'type' => $item['type'],
                'description' => $item['description'],
                'timestamp' => $item['timestamp']
            ];
        }, $recentActivity)
    ];
    
    sendJsonResponse($response);
    
} catch (Exception $e) {
    logError('Admin stats error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
