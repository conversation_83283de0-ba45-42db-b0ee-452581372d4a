<?php
/**
 * Get Messages List API
 */

try {
    // Only allow GET requests
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $groupId = $_GET['group_id'] ?? null;
    $currentUserId = getCurrentUserId();
    $pagination = paginate($_GET['page'] ?? 1, $_GET['limit'] ?? 50);
    
    if (!$groupId) {
        sendErrorResponse('Group ID is required');
    }
    
    $db = Database::getInstance();
    
    // Check if user is a member of the group
    $membership = $db->fetchOne(
        "SELECT role FROM group_members WHERE group_id = ? AND user_id = ?",
        [$groupId, $currentUserId]
    );
    
    if (!$membership) {
        sendErrorResponse('You are not a member of this group', 403);
    }
    
    // Get messages
    $messages = $db->fetchAll("
        SELECT 
            m.*,
            u.username,
            u.first_name,
            u.last_name,
            u.profile_image_url
        FROM messages m
        JOIN users u ON m.user_id = u.id
        WHERE m.group_id = ? AND m.is_deleted = 0
        ORDER BY m.created_at DESC
        LIMIT ? OFFSET ?
    ", [$groupId, $pagination['limit'], $pagination['offset']]);
    
    // Format messages data
    $formattedMessages = [];
    foreach (array_reverse($messages) as $message) {
        $messageUser = [
            'id' => $message['user_id'],
            'username' => $message['username'],
            'firstName' => $message['first_name'],
            'lastName' => $message['last_name'],
            'profileImageUrl' => $message['profile_image_url']
        ];
        
        $formattedMessages[] = [
            'id' => $message['id'],
            'groupId' => $message['group_id'],
            'userId' => $message['user_id'],
            'content' => $message['content'],
            'messageType' => $message['message_type'],
            'isOneTime' => (bool)$message['is_one_time'],
            'createdAt' => $message['created_at'],
            'user' => $messageUser
        ];
    }
    
    sendJsonResponse([
        'messages' => $formattedMessages,
        'pagination' => [
            'page' => $pagination['page'],
            'limit' => $pagination['limit'],
            'hasMore' => count($messages) === $pagination['limit']
        ]
    ]);
    
} catch (Exception $e) {
    logError('Get messages error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
