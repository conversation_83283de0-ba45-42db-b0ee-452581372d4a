import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { loginUser, registerUser } from "@/lib/authUtils";
import { useToast } from "@/hooks/use-toast";

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function AuthModal({ isOpen, onClose }: AuthModalProps) {
  const [isLogin, setIsLogin] = useState(true);
  const [step, setStep] = useState(1);
  const [formData, setFormData] = useState({
    phoneNumber: "",
    countryCode: "+1",
    password: "",
    confirmPassword: "",
    firstName: "",
    lastName: "",
    email: "",
    country: "",
    age: "",
    languagesSpoken: [] as string[],
    languagesLearning: [] as string[],
    interests: [] as string[],
    bio: "",
    status: "Student",
  });

  const { toast } = useToast();
  const queryClient = useQueryClient();

  const loginMutation = useMutation({
    mutationFn: ({ phoneNumber, password }: { phoneNumber: string; password: string }) =>
      loginUser(phoneNumber, password),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["auth", "user"] });
      toast({ title: "Welcome back!", description: "You've been logged in successfully." });
      onClose();
    },
    onError: (error: Error) => {
      toast({ title: "Login failed", description: error.message, variant: "destructive" });
    },
  });

  const registerMutation = useMutation({
    mutationFn: (userData: any) => registerUser(userData),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["auth", "user"] });
      toast({ title: "Welcome!", description: "Your account has been created successfully." });
      onClose();
    },
    onError: (error: Error) => {
      toast({ title: "Registration failed", description: error.message, variant: "destructive" });
    },
  });

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleArrayChange = (field: string, value: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      [field]: checked 
        ? [...prev[field as keyof typeof prev] as string[], value]
        : (prev[field as keyof typeof prev] as string[]).filter(item => item !== value)
    }));
  };

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.phoneNumber || !formData.password) {
      toast({ title: "Error", description: "Please fill in all required fields.", variant: "destructive" });
      return;
    }
    loginMutation.mutate({ phoneNumber: formData.phoneNumber, password: formData.password });
  };

  const handleRegisterStep1 = (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.phoneNumber || !formData.password || !formData.firstName || !formData.country) {
      toast({ title: "Error", description: "Please fill in all required fields.", variant: "destructive" });
      return;
    }
    if (formData.password !== formData.confirmPassword) {
      toast({ title: "Error", description: "Passwords do not match.", variant: "destructive" });
      return;
    }
    if (formData.age && (parseInt(formData.age) < 13 || parseInt(formData.age) > 19)) {
      toast({ title: "Error", description: "Age must be between 13 and 19.", variant: "destructive" });
      return;
    }
    setStep(2);
  };

  const handleRegisterStep2 = (e: React.FormEvent) => {
    e.preventDefault();
    const userData = {
      ...formData,
      age: formData.age ? parseInt(formData.age) : undefined,
    };
    registerMutation.mutate(userData);
  };

  const countries = [
    "United States", "Canada", "United Kingdom", "Australia", "Germany", "France", 
    "Spain", "Italy", "Japan", "South Korea", "China", "India", "Brazil", "Mexico"
  ];

  const languages = [
    "English", "Spanish", "French", "German", "Italian", "Portuguese", "Chinese", 
    "Japanese", "Korean", "Arabic", "Russian", "Hindi", "Dutch", "Swedish"
  ];

  const interestOptions = [
    "Music", "Movies", "Sports", "Gaming", "Reading", "Travel", "Cooking", 
    "Art", "Technology", "Fashion", "Photography", "Dancing", "Fitness"
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <CardTitle className="text-center">
            {isLogin ? "Welcome Back!" : step === 1 ? "Join LinguaVibe" : "Complete Your Profile"}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {isLogin ? (
            <form onSubmit={handleLogin} className="space-y-4">
              <div>
                <Label htmlFor="phoneNumber">Phone Number *</Label>
                <div className="flex space-x-2">
                  <Select value={formData.countryCode} onValueChange={(value) => handleInputChange("countryCode", value)}>
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="+1">+1</SelectItem>
                      <SelectItem value="+44">+44</SelectItem>
                      <SelectItem value="+49">+49</SelectItem>
                      <SelectItem value="+33">+33</SelectItem>
                      <SelectItem value="+81">+81</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    id="phoneNumber"
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                    placeholder="1234567890"
                    className="flex-1"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="password">Password *</Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => handleInputChange("password", e.target.value)}
                />
              </div>
              <Button type="submit" className="w-full" disabled={loginMutation.isPending}>
                {loginMutation.isPending ? "Logging in..." : "Login"}
              </Button>
              <p className="text-center text-sm">
                Don't have an account?{" "}
                <button type="button" onClick={() => setIsLogin(false)} className="text-primary hover:underline">
                  Sign up
                </button>
              </p>
            </form>
          ) : step === 1 ? (
            <form onSubmit={handleRegisterStep1} className="space-y-4">
              <div>
                <Label htmlFor="firstName">First Name *</Label>
                <Input
                  id="firstName"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange("firstName", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="lastName">Last Name</Label>
                <Input
                  id="lastName"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange("lastName", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange("email", e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="phoneNumber">Phone Number *</Label>
                <div className="flex space-x-2">
                  <Select value={formData.countryCode} onValueChange={(value) => handleInputChange("countryCode", value)}>
                    <SelectTrigger className="w-20">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="+1">+1</SelectItem>
                      <SelectItem value="+44">+44</SelectItem>
                      <SelectItem value="+49">+49</SelectItem>
                      <SelectItem value="+33">+33</SelectItem>
                      <SelectItem value="+81">+81</SelectItem>
                    </SelectContent>
                  </Select>
                  <Input
                    id="phoneNumber"
                    type="tel"
                    value={formData.phoneNumber}
                    onChange={(e) => handleInputChange("phoneNumber", e.target.value)}
                    placeholder="1234567890"
                    className="flex-1"
                  />
                </div>
              </div>
              <div>
                <Label htmlFor="country">Country *</Label>
                <Select value={formData.country} onValueChange={(value) => handleInputChange("country", value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select your country" />
                  </SelectTrigger>
                  <SelectContent>
                    {countries.map(country => (
                      <SelectItem key={country} value={country}>{country}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="age">Age</Label>
                <Input
                  id="age"
                  type="number"
                  min="13"
                  max="19"
                  value={formData.age}
                  onChange={(e) => handleInputChange("age", e.target.value)}
                  placeholder="Optional"
                />
              </div>
              <div>
                <Label htmlFor="password">Password *</Label>
                <Input
                  id="password"
                  type="password"
                  value={formData.password}
                  onChange={(e) => handleInputChange("password", e.target.value)}
                  placeholder="At least 6 characters"
                />
              </div>
              <div>
                <Label htmlFor="confirmPassword">Confirm Password *</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={formData.confirmPassword}
                  onChange={(e) => handleInputChange("confirmPassword", e.target.value)}
                />
              </div>
              <Button type="submit" className="w-full">
                Continue
              </Button>
              <p className="text-center text-sm">
                Already have an account?{" "}
                <button type="button" onClick={() => setIsLogin(true)} className="text-primary hover:underline">
                  Login
                </button>
              </p>
            </form>
          ) : (
            <form onSubmit={handleRegisterStep2} className="space-y-4">
              <div>
                <Label>Languages You Speak</Label>
                <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                  {languages.map(lang => (
                    <div key={lang} className="flex items-center space-x-2">
                      <Checkbox
                        id={`spoken-${lang}`}
                        checked={formData.languagesSpoken.includes(lang)}
                        onCheckedChange={(checked) => handleArrayChange("languagesSpoken", lang, checked as boolean)}
                      />
                      <Label htmlFor={`spoken-${lang}`} className="text-sm">{lang}</Label>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <Label>Languages You Want to Learn</Label>
                <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                  {languages.map(lang => (
                    <div key={lang} className="flex items-center space-x-2">
                      <Checkbox
                        id={`learning-${lang}`}
                        checked={formData.languagesLearning.includes(lang)}
                        onCheckedChange={(checked) => handleArrayChange("languagesLearning", lang, checked as boolean)}
                      />
                      <Label htmlFor={`learning-${lang}`} className="text-sm">{lang}</Label>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <Label>Interests</Label>
                <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                  {interestOptions.map(interest => (
                    <div key={interest} className="flex items-center space-x-2">
                      <Checkbox
                        id={`interest-${interest}`}
                        checked={formData.interests.includes(interest)}
                        onCheckedChange={(checked) => handleArrayChange("interests", interest, checked as boolean)}
                      />
                      <Label htmlFor={`interest-${interest}`} className="text-sm">{interest}</Label>
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <Label htmlFor="status">Status</Label>
                <Select value={formData.status} onValueChange={(value) => handleInputChange("status", value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Student">Student</SelectItem>
                    <SelectItem value="Working">Working</SelectItem>
                    <SelectItem value="Learning">Learning</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="bio">Bio</Label>
                <Textarea
                  id="bio"
                  value={formData.bio}
                  onChange={(e) => handleInputChange("bio", e.target.value)}
                  placeholder="Tell us about yourself..."
                  rows={3}
                />
              </div>
              <div className="flex space-x-2">
                <Button type="button" variant="outline" onClick={() => setStep(1)} className="flex-1">
                  Back
                </Button>
                <Button type="submit" className="flex-1" disabled={registerMutation.isPending}>
                  {registerMutation.isPending ? "Creating Account..." : "Create Account"}
                </Button>
              </div>
            </form>
          )}
          <Button variant="ghost" onClick={onClose} className="w-full">
            Cancel
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
