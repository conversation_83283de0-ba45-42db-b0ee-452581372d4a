#!/bin/bash

echo "Starting TeenLanguageConnect PHP Backend..."
echo

# Check if P<PERSON> is installed
if ! command -v php &> /dev/null; then
    echo "ERROR: PHP is not installed"
    echo "Please install PHP 7.4 or higher"
    exit 1
fi

echo "PHP version:"
php --version
echo

# Check if MySQL is running
if ! command -v mysql &> /dev/null; then
    echo "WARNING: MySQL command not found. Make sure MySQL is installed and running."
    echo "You can continue if MySQL is running on a different setup."
    echo
else
    echo "MySQL version:"
    mysql --version
    echo
fi

# Create database if it doesn't exist
echo "Setting up database..."
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS teen_language_connect CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Note: Could not create database automatically. Please create it manually:"
    echo "  CREATE DATABASE teen_language_connect CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    echo
fi

# Import database schema
echo "Importing database schema..."
mysql -u root -p teen_language_connect < php-backend/setup.sql 2>/dev/null
if [ $? -ne 0 ]; then
    echo "Note: Could not import schema automatically. Please run:"
    echo "  mysql -u root -p teen_language_connect < php-backend/setup.sql"
    echo
fi

# Start WebSocket server in background
echo "Starting WebSocket server in background..."
php php-backend/websocket/server.php &
WEBSOCKET_PID=$!

# Function to cleanup on exit
cleanup() {
    echo
    echo "Shutting down servers..."
    kill $WEBSOCKET_PID 2>/dev/null
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start PHP development server
echo
echo "Starting PHP development server on http://localhost:8000"
echo "Admin panel will be available at: http://localhost:8000/php-backend/admin"
echo
echo "Pre-built Admin Credentials:"
echo "  Phone: +1234567890"
echo "  Password: Admin123!@#"
echo "  Email: <EMAIL>"
echo
echo "Press Ctrl+C to stop all servers"
echo

cd php-backend
php -S localhost:8000

# Cleanup when PHP server stops
cleanup
