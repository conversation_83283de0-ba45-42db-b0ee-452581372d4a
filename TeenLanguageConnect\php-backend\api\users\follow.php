<?php
/**
 * Follow User API
 */

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $followingId = $_POST['user_id'] ?? null;
    $followerId = getCurrentUserId();
    
    if (!$followingId) {
        sendErrorResponse('User ID is required');
    }
    
    if ($followerId === $followingId) {
        sendErrorResponse('Cannot follow yourself');
    }
    
    $db = Database::getInstance();
    
    // Check if target user exists and is not blocked
    $targetUser = $db->fetchOne(
        "SELECT id FROM users WHERE id = ? AND is_blocked = 0",
        [$followingId]
    );
    
    if (!$targetUser) {
        sendErrorResponse('User not found', 404);
    }
    
    // Check if already following
    $existingFollow = $db->fetchOne(
        "SELECT id FROM follows WHERE follower_id = ? AND following_id = ?",
        [$followerId, $followingId]
    );
    
    if ($existingFollow) {
        sendErrorResponse('Already following this user');
    }
    
    // Check if blocked by target user
    $isBlocked = $db->fetchOne(
        "SELECT id FROM blocked_users WHERE blocker_id = ? AND blocked_id = ?",
        [$followingId, $followerId]
    );
    
    if ($isBlocked) {
        sendErrorResponse('Cannot follow this user');
    }
    
    // Create follow relationship
    $db->insert('follows', [
        'follower_id' => $followerId,
        'following_id' => $followingId
    ]);
    
    // Create notification for followed user
    $followerUser = $db->fetchOne("SELECT first_name, last_name FROM users WHERE id = ?", [$followerId]);
    $followerName = trim($followerUser['first_name'] . ' ' . $followerUser['last_name']);
    
    $db->insert('notifications', [
        'user_id' => $followingId,
        'type' => 'follow',
        'title' => 'New Follower',
        'message' => "{$followerName} started following you",
        'related_id' => $followerId
    ]);
    
    sendJsonResponse([
        'message' => 'User followed successfully'
    ]);
    
} catch (Exception $e) {
    logError('Follow user error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
