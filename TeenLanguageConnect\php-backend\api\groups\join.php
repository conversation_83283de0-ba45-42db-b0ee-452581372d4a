<?php
/**
 * Join Group API
 */

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $groupId = $_POST['group_id'] ?? null;
    $currentUserId = getCurrentUserId();
    
    if (!$groupId) {
        sendErrorResponse('Group ID is required');
    }
    
    $db = Database::getInstance();
    
    // Check if group exists and is active
    $group = $db->fetchOne(
        "SELECT * FROM groups WHERE id = ? AND is_active = 1",
        [$groupId]
    );
    
    if (!$group) {
        sendErrorResponse('Group not found or inactive', 404);
    }
    
    // Check if user is already a member
    $existingMembership = $db->fetchOne(
        "SELECT role FROM group_members WHERE group_id = ? AND user_id = ?",
        [$groupId, $currentUserId]
    );
    
    if ($existingMembership) {
        sendErrorResponse('Already a member of this group');
    }
    
    // Check if group is full (for participants)
    if ($group['current_participants'] >= $group['max_participants']) {
        // Join as listener instead
        $role = 'listener';
    } else {
        // Join as participant
        $role = 'participant';
    }
    
    // Add user to group
    $db->insert('group_members', [
        'group_id' => $groupId,
        'user_id' => $currentUserId,
        'role' => $role
    ]);
    
    // Create notification for group admin
    $user = $db->fetchOne("SELECT first_name, last_name FROM users WHERE id = ?", [$currentUserId]);
    $userName = trim($user['first_name'] . ' ' . $user['last_name']);
    
    $db->insert('notifications', [
        'user_id' => $group['admin_id'],
        'type' => 'group_invite',
        'title' => 'New Group Member',
        'message' => "{$userName} joined your group '{$group['name']}'",
        'related_id' => $groupId
    ]);
    
    sendJsonResponse([
        'message' => "Successfully joined group as {$role}",
        'role' => $role
    ]);
    
} catch (Exception $e) {
    logError('Join group error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
