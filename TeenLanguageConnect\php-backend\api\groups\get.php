<?php
/**
 * Get Single Group API
 */

try {
    // Only allow GET requests
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $groupId = $_GET['group_id'] ?? null;
    $currentUserId = getCurrentUserId();
    
    if (!$groupId) {
        sendErrorResponse('Group ID is required');
    }
    
    $db = Database::getInstance();
    
    // Get group with admin info and user membership status
    $group = $db->fetchOne("
        SELECT 
            g.*,
            u.username as admin_username,
            u.first_name as admin_first_name,
            u.last_name as admin_last_name,
            u.profile_image_url as admin_profile_image,
            (SELECT role FROM group_members gm WHERE gm.group_id = g.id AND gm.user_id = ?) as user_role
        FROM groups g
        JOIN users u ON g.admin_id = u.id
        WHERE g.id = ? AND g.is_active = 1
    ", [$currentUserId, $groupId]);
    
    if (!$group) {
        sendErrorResponse('Group not found', 404);
    }
    
    // Get group members
    $members = $db->fetchAll("
        SELECT 
            gm.role,
            gm.joined_at,
            u.id,
            u.username,
            u.first_name,
            u.last_name,
            u.profile_image_url,
            u.country
        FROM group_members gm
        JOIN users u ON gm.user_id = u.id
        WHERE gm.group_id = ?
        ORDER BY 
            CASE gm.role 
                WHEN 'admin' THEN 1 
                WHEN 'participant' THEN 2 
                WHEN 'listener' THEN 3 
            END,
            gm.joined_at ASC
    ", [$groupId]);
    
    // Get recent messages (last 50)
    $messages = $db->fetchAll("
        SELECT 
            m.*,
            u.username,
            u.first_name,
            u.last_name,
            u.profile_image_url
        FROM messages m
        JOIN users u ON m.user_id = u.id
        WHERE m.group_id = ? AND m.is_deleted = 0
        ORDER BY m.created_at DESC
        LIMIT 50
    ", [$groupId]);
    
    // Format admin data
    $adminData = [
        'id' => $group['admin_id'],
        'username' => $group['admin_username'],
        'firstName' => $group['admin_first_name'],
        'lastName' => $group['admin_last_name'],
        'profileImageUrl' => $group['admin_profile_image']
    ];
    
    // Format members data
    $formattedMembers = [];
    foreach ($members as $member) {
        $formattedMembers[] = [
            'id' => $member['id'],
            'username' => $member['username'],
            'firstName' => $member['first_name'],
            'lastName' => $member['last_name'],
            'profileImageUrl' => $member['profile_image_url'],
            'country' => $member['country'],
            'role' => $member['role'],
            'joinedAt' => $member['joined_at']
        ];
    }
    
    // Format messages data
    $formattedMessages = [];
    foreach (array_reverse($messages) as $message) {
        $messageUser = [
            'id' => $message['user_id'],
            'username' => $message['username'],
            'firstName' => $message['first_name'],
            'lastName' => $message['last_name'],
            'profileImageUrl' => $message['profile_image_url']
        ];
        
        $formattedMessages[] = [
            'id' => $message['id'],
            'groupId' => $message['group_id'],
            'userId' => $message['user_id'],
            'content' => $message['content'],
            'messageType' => $message['message_type'],
            'isOneTime' => (bool)$message['is_one_time'],
            'createdAt' => $message['created_at'],
            'user' => $messageUser
        ];
    }
    
    // Format group data
    $formattedGroup = [
        'id' => $group['id'],
        'name' => $group['name'],
        'description' => $group['description'],
        'language' => $group['language'],
        'adminId' => $group['admin_id'],
        'maxParticipants' => $group['max_participants'],
        'currentParticipants' => $group['current_participants'],
        'isActive' => (bool)$group['is_active'],
        'isPrivate' => (bool)$group['is_private'],
        'createdAt' => $group['created_at'],
        'admin' => $adminData,
        'userRole' => $group['user_role'],
        'isFull' => $group['current_participants'] >= $group['max_participants'],
        'members' => $formattedMembers,
        'messages' => $formattedMessages
    ];
    
    sendJsonResponse($formattedGroup);
    
} catch (Exception $e) {
    logError('Get group error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
