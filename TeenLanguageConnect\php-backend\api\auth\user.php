<?php
/**
 * Get Current User API
 */

try {
    // Only allow GET requests
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Check if session is valid
    if (!validateSession()) {
        sendErrorResponse('Session expired', 401);
    }
    
    // Get current user
    $user = getCurrentUser();
    
    if (!$user) {
        sendErrorResponse('User not found', 404);
    }
    
    sendJsonResponse($user);
    
} catch (Exception $e) {
    logError('Get user error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
