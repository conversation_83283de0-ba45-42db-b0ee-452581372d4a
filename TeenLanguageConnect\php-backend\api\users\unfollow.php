<?php
/**
 * Unfollow User API
 */

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $followingId = $_POST['user_id'] ?? null;
    $followerId = getCurrentUserId();
    
    if (!$followingId) {
        sendErrorResponse('User ID is required');
    }
    
    if ($followerId === $followingId) {
        sendErrorResponse('Cannot unfollow yourself');
    }
    
    $db = Database::getInstance();
    
    // Check if currently following
    $existingFollow = $db->fetchOne(
        "SELECT id FROM follows WHERE follower_id = ? AND following_id = ?",
        [$followerId, $followingId]
    );
    
    if (!$existingFollow) {
        sendErrorResponse('Not following this user');
    }
    
    // Remove follow relationship
    $db->delete('follows', 'follower_id = ? AND following_id = ?', [$followerId, $followingId]);
    
    sendJsonResponse([
        'message' => 'User unfollowed successfully'
    ]);
    
} catch (Exception $e) {
    logError('Unfollow user error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
