import { QueryClient } from "@tanstack/react-query";
import { apiCall, API_BASE_URL } from "./authUtils";

export async function apiRequest(
  method: string,
  endpoint: string,
  data?: unknown | undefined,
): Promise<any> {
  const options: RequestInit = {
    method,
    ...(data && { body: JSON.stringify(data) }),
  };

  return apiCall(endpoint, options);
}

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchInterval: false,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
      retry: (failureCount, error) => {
        // Don't retry on 401 (unauthorized) or 403 (forbidden)
        if (error.message.includes('401') || error.message.includes('403')) {
          return false;
        }
        return failureCount < 3;
      },
    },
    mutations: {
      retry: false,
    },
  },
});
