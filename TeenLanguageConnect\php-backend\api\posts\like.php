<?php
/**
 * Like/Unlike Post API
 */

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $postId = $_POST['post_id'] ?? null;
    $currentUserId = getCurrentUserId();
    
    if (!$postId) {
        sendErrorResponse('Post ID is required');
    }
    
    $db = Database::getInstance();
    
    // Check if post exists
    $post = $db->fetchOne(
        "SELECT user_id FROM posts WHERE id = ?",
        [$postId]
    );
    
    if (!$post) {
        sendErrorResponse('Post not found', 404);
    }
    
    // Check if already liked
    $existingLike = $db->fetchOne(
        "SELECT id FROM likes WHERE user_id = ? AND post_id = ?",
        [$currentUserId, $postId]
    );
    
    if ($existingLike) {
        // Unlike the post
        $db->delete('likes', 'user_id = ? AND post_id = ?', [$currentUserId, $postId]);
        $action = 'unliked';
        $liked = false;
    } else {
        // Like the post
        $db->insert('likes', [
            'user_id' => $currentUserId,
            'post_id' => $postId
        ]);
        $action = 'liked';
        $liked = true;
        
        // Create notification for post owner (if not own post)
        if ($post['user_id'] !== $currentUserId) {
            $liker = $db->fetchOne("SELECT first_name, last_name FROM users WHERE id = ?", [$currentUserId]);
            $likerName = trim($liker['first_name'] . ' ' . $liker['last_name']);
            
            $db->insert('notifications', [
                'user_id' => $post['user_id'],
                'type' => 'like',
                'title' => 'New Like',
                'message' => "{$likerName} liked your post",
                'related_id' => $postId
            ]);
        }
    }
    
    // Get updated likes count
    $likesCount = $db->fetchOne(
        "SELECT likes_count FROM posts WHERE id = ?",
        [$postId]
    )['likes_count'];
    
    sendJsonResponse([
        'message' => "Post {$action} successfully",
        'liked' => $liked,
        'likesCount' => (int)$likesCount
    ]);
    
} catch (Exception $e) {
    logError('Like post error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
