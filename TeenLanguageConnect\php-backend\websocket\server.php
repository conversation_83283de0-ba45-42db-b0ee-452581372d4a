<?php
/**
 * WebSocket Server for Real-time Chat
 * Run this script separately: php websocket/server.php
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

class ChatWebSocketServer {
    private $clients = [];
    private $groupRooms = [];
    private $userSessions = [];
    
    public function __construct($host = 'localhost', $port = 8080) {
        $this->host = $host;
        $this->port = $port;
        $this->socket = socket_create(AF_INET, SOCK_STREAM, SOL_TCP);
        
        socket_set_option($this->socket, SOL_SOCKET, SO_REUSEADDR, 1);
        socket_bind($this->socket, $this->host, $this->port);
        socket_listen($this->socket);
        
        echo "WebSocket server started on {$this->host}:{$this->port}\n";
    }
    
    public function run() {
        while (true) {
            $read = array_merge([$this->socket], $this->clients);
            $write = null;
            $except = null;
            
            if (socket_select($read, $write, $except, 0, 10000) < 1) {
                continue;
            }
            
            if (in_array($this->socket, $read)) {
                $newClient = socket_accept($this->socket);
                $this->clients[] = $newClient;
                $this->performHandshake($newClient);
                echo "New client connected\n";
            }
            
            foreach ($this->clients as $key => $client) {
                if (in_array($client, $read)) {
                    $data = socket_read($client, 1024);
                    
                    if ($data === false || $data === '') {
                        $this->disconnectClient($key);
                        continue;
                    }
                    
                    $message = $this->unmask($data);
                    $this->handleMessage($client, $message);
                }
            }
        }
    }
    
    private function performHandshake($client) {
        $request = socket_read($client, 5000);
        preg_match('#Sec-WebSocket-Key: (.*)\r\n#', $request, $matches);
        
        if (empty($matches[1])) {
            socket_close($client);
            return false;
        }
        
        $key = $matches[1];
        $responseKey = base64_encode(pack('H*', sha1($key . '258EAFA5-E914-47DA-95CA-C5AB0DC85B11')));
        
        $response = "HTTP/1.1 101 Switching Protocols\r\n" .
                   "Upgrade: websocket\r\n" .
                   "Connection: Upgrade\r\n" .
                   "Sec-WebSocket-Accept: {$responseKey}\r\n\r\n";
        
        socket_write($client, $response, strlen($response));
        return true;
    }
    
    private function handleMessage($client, $message) {
        $data = json_decode($message, true);
        
        if (!$data || !isset($data['type'])) {
            return;
        }
        
        switch ($data['type']) {
            case 'auth':
                $this->handleAuth($client, $data);
                break;
                
            case 'join_group':
                $this->handleJoinGroup($client, $data);
                break;
                
            case 'leave_group':
                $this->handleLeaveGroup($client, $data);
                break;
                
            case 'group_message':
                $this->handleGroupMessage($client, $data);
                break;
                
            case 'typing':
                $this->handleTyping($client, $data);
                break;
                
            case 'ping':
                $this->sendToClient($client, ['type' => 'pong']);
                break;
        }
    }
    
    private function handleAuth($client, $data) {
        if (!isset($data['userId']) || !isset($data['sessionId'])) {
            $this->sendToClient($client, ['type' => 'auth_error', 'message' => 'Invalid auth data']);
            return;
        }
        
        // Verify user session (simplified - in production, verify against database)
        $this->userSessions[spl_object_hash($client)] = [
            'userId' => $data['userId'],
            'sessionId' => $data['sessionId'],
            'groups' => []
        ];
        
        $this->sendToClient($client, ['type' => 'auth_success']);
        echo "User {$data['userId']} authenticated\n";
    }
    
    private function handleJoinGroup($client, $data) {
        $clientHash = spl_object_hash($client);
        
        if (!isset($this->userSessions[$clientHash])) {
            $this->sendToClient($client, ['type' => 'error', 'message' => 'Not authenticated']);
            return;
        }
        
        $groupId = $data['groupId'];
        $userId = $this->userSessions[$clientHash]['userId'];
        
        // Verify user is member of group (check database)
        $db = Database::getInstance();
        $membership = $db->fetchOne(
            "SELECT role FROM group_members WHERE group_id = ? AND user_id = ?",
            [$groupId, $userId]
        );
        
        if (!$membership) {
            $this->sendToClient($client, ['type' => 'error', 'message' => 'Not a member of this group']);
            return;
        }
        
        // Add to group room
        if (!isset($this->groupRooms[$groupId])) {
            $this->groupRooms[$groupId] = [];
        }
        
        $this->groupRooms[$groupId][$clientHash] = $client;
        $this->userSessions[$clientHash]['groups'][] = $groupId;
        
        // Notify group members
        $this->broadcastToGroup($groupId, [
            'type' => 'user_joined',
            'userId' => $userId,
            'groupId' => $groupId
        ], $clientHash);
        
        $this->sendToClient($client, ['type' => 'joined_group', 'groupId' => $groupId]);
        echo "User {$userId} joined group {$groupId}\n";
    }
    
    private function handleLeaveGroup($client, $data) {
        $clientHash = spl_object_hash($client);
        $groupId = $data['groupId'];
        
        if (isset($this->groupRooms[$groupId][$clientHash])) {
            unset($this->groupRooms[$groupId][$clientHash]);
            
            // Remove from user session
            if (isset($this->userSessions[$clientHash])) {
                $groups = &$this->userSessions[$clientHash]['groups'];
                $groups = array_filter($groups, function($g) use ($groupId) {
                    return $g !== $groupId;
                });
                
                // Notify group members
                $this->broadcastToGroup($groupId, [
                    'type' => 'user_left',
                    'userId' => $this->userSessions[$clientHash]['userId'],
                    'groupId' => $groupId
                ]);
            }
        }
        
        $this->sendToClient($client, ['type' => 'left_group', 'groupId' => $groupId]);
    }
    
    private function handleGroupMessage($client, $data) {
        $clientHash = spl_object_hash($client);
        
        if (!isset($this->userSessions[$clientHash])) {
            return;
        }
        
        $groupId = $data['groupId'];
        $userId = $this->userSessions[$clientHash]['userId'];
        
        // Verify user is in the group room
        if (!isset($this->groupRooms[$groupId][$clientHash])) {
            return;
        }
        
        // Save message to database
        $db = Database::getInstance();
        $messageId = $db->insert('messages', [
            'group_id' => $groupId,
            'user_id' => $userId,
            'content' => $data['content'],
            'message_type' => $data['messageType'] ?? 'text'
        ]);
        
        // Get user info for broadcast
        $user = $db->fetchOne("SELECT username, first_name, last_name, profile_image_url FROM users WHERE id = ?", [$userId]);
        
        // Broadcast to all group members
        $messageData = [
            'type' => 'new_message',
            'id' => $messageId,
            'groupId' => $groupId,
            'userId' => $userId,
            'content' => $data['content'],
            'messageType' => $data['messageType'] ?? 'text',
            'createdAt' => date('Y-m-d H:i:s'),
            'user' => [
                'id' => $userId,
                'username' => $user['username'],
                'firstName' => $user['first_name'],
                'lastName' => $user['last_name'],
                'profileImageUrl' => $user['profile_image_url']
            ]
        ];
        
        $this->broadcastToGroup($groupId, $messageData);
        echo "Message sent in group {$groupId} by user {$userId}\n";
    }
    
    private function handleTyping($client, $data) {
        $clientHash = spl_object_hash($client);
        
        if (!isset($this->userSessions[$clientHash])) {
            return;
        }
        
        $groupId = $data['groupId'];
        $userId = $this->userSessions[$clientHash]['userId'];
        
        // Broadcast typing indicator to group (except sender)
        $this->broadcastToGroup($groupId, [
            'type' => 'user_typing',
            'userId' => $userId,
            'groupId' => $groupId,
            'isTyping' => $data['isTyping'] ?? true
        ], $clientHash);
    }
    
    private function broadcastToGroup($groupId, $message, $excludeClient = null) {
        if (!isset($this->groupRooms[$groupId])) {
            return;
        }
        
        foreach ($this->groupRooms[$groupId] as $clientHash => $client) {
            if ($excludeClient && $clientHash === $excludeClient) {
                continue;
            }
            
            $this->sendToClient($client, $message);
        }
    }
    
    private function sendToClient($client, $data) {
        $message = json_encode($data);
        $frame = $this->mask($message);
        socket_write($client, $frame, strlen($frame));
    }
    
    private function disconnectClient($key) {
        $client = $this->clients[$key];
        $clientHash = spl_object_hash($client);
        
        // Remove from all group rooms
        foreach ($this->groupRooms as $groupId => &$room) {
            if (isset($room[$clientHash])) {
                unset($room[$clientHash]);
                
                // Notify group members
                if (isset($this->userSessions[$clientHash])) {
                    $this->broadcastToGroup($groupId, [
                        'type' => 'user_left',
                        'userId' => $this->userSessions[$clientHash]['userId'],
                        'groupId' => $groupId
                    ]);
                }
            }
        }
        
        // Remove user session
        unset($this->userSessions[$clientHash]);
        
        // Close and remove client
        socket_close($client);
        unset($this->clients[$key]);
        
        echo "Client disconnected\n";
    }
    
    private function mask($text) {
        $b1 = 0x80 | (0x1 & 0x0f);
        $length = strlen($text);
        
        if ($length <= 125) {
            $header = pack('CC', $b1, $length);
        } elseif ($length > 125 && $length < 65536) {
            $header = pack('CCn', $b1, 126, $length);
        } else {
            $header = pack('CCNN', $b1, 127, $length);
        }
        
        return $header . $text;
    }
    
    private function unmask($text) {
        $length = ord($text[1]) & 127;
        
        if ($length == 126) {
            $masks = substr($text, 4, 4);
            $data = substr($text, 8);
        } elseif ($length == 127) {
            $masks = substr($text, 10, 4);
            $data = substr($text, 14);
        } else {
            $masks = substr($text, 2, 4);
            $data = substr($text, 6);
        }
        
        $text = "";
        for ($i = 0; $i < strlen($data); ++$i) {
            $text .= $data[$i] ^ $masks[$i % 4];
        }
        
        return $text;
    }
}

// Start the WebSocket server
$server = new ChatWebSocketServer(WEBSOCKET_HOST, WEBSOCKET_PORT);
$server->run();
?>
