<?php
/**
 * User Registration API
 */

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Rate limiting
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    checkRateLimit('register_' . $clientIp, 5, 3600); // 5 attempts per hour
    
    // Get JSON input
    $input = getJsonInput();
    
    // Validate required fields
    $required = ['phoneNumber', 'countryCode', 'country', 'firstName', 'password'];
    foreach ($required as $field) {
        if (empty($input[$field])) {
            sendErrorResponse("Field {$field} is required");
        }
    }
    
    // Validate password strength
    if (strlen($input['password']) < 6) {
        sendErrorResponse('Password must be at least 6 characters long');
    }
    
    // Validate age if provided
    if (isset($input['age']) && ($input['age'] < 13 || $input['age'] > 19)) {
        sendErrorResponse('Age must be between 13 and 19 for teen platform');
    }
    
    // Register user
    $userId = registerUser($input);
    
    // Auto-login after registration
    $user = loginUser($input['phoneNumber'], $input['password']);
    
    sendJsonResponse([
        'message' => 'Registration successful',
        'user' => $user
    ], 201);
    
} catch (Exception $e) {
    logError('Registration error: ' . $e->getMessage(), $input ?? []);
    sendErrorResponse($e->getMessage());
}
?>
