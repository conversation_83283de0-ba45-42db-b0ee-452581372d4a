<?php
/**
 * User Login API
 */

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Rate limiting
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    checkRateLimit('login_' . $clientIp, 10, 3600); // 10 attempts per hour
    
    // Get JSON input
    $input = getJsonInput();
    
    // Validate required fields
    if (empty($input['phoneNumber']) || empty($input['password'])) {
        sendErrorResponse('Phone number and password are required');
    }
    
    // Attempt login
    $user = loginUser($input['phoneNumber'], $input['password']);
    
    sendJsonResponse([
        'message' => 'Login successful',
        'user' => $user
    ]);
    
} catch (Exception $e) {
    logError('Login error: ' . $e->getMessage(), [
        'phoneNumber' => $input['phoneNumber'] ?? 'unknown',
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown'
    ]);
    sendErrorResponse($e->getMessage(), 401);
}
?>
