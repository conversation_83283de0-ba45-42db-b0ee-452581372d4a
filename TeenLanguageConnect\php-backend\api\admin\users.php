<?php
/**
 * Admin Users Management API
 */

try {
    // Only allow GET requests
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require admin authentication
    requireAdmin();
    
    $pagination = paginate($_GET['page'] ?? 1, $_GET['limit'] ?? 20);
    $search = $_GET['search'] ?? null;
    $status = $_GET['status'] ?? null; // 'active', 'blocked'
    
    $db = Database::getInstance();
    
    // Build query conditions
    $conditions = [];
    $params = [];
    
    if ($search) {
        $conditions[] = '(first_name LIKE ? OR last_name LIKE ? OR username LIKE ? OR email LIKE ? OR phone_number LIKE ?)';
        $searchTerm = "%{$search}%";
        $params = array_merge($params, [$searchTerm, $searchTerm, $searchTerm, $searchTerm, $searchTerm]);
    }
    
    if ($status === 'blocked') {
        $conditions[] = 'is_blocked = 1';
    } elseif ($status === 'active') {
        $conditions[] = 'is_blocked = 0';
    }
    
    $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
    
    // Get users with stats
    $users = $db->fetchAll("
        SELECT 
            u.*,
            (SELECT COUNT(*) FROM posts WHERE user_id = u.id) as posts_count,
            (SELECT COUNT(*) FROM follows WHERE following_id = u.id) as followers_count,
            (SELECT COUNT(*) FROM follows WHERE follower_id = u.id) as following_count,
            (SELECT COUNT(*) FROM groups WHERE admin_id = u.id) as groups_count,
            (SELECT COUNT(*) FROM reports WHERE reported_user_id = u.id) as reports_count
        FROM users u
        {$whereClause}
        ORDER BY u.created_at DESC
        LIMIT ? OFFSET ?
    ", array_merge($params, [$pagination['limit'], $pagination['offset']]));
    
    // Get total count for pagination
    $totalCount = $db->fetchOne("
        SELECT COUNT(*) as count FROM users u {$whereClause}
    ", $params)['count'];
    
    // Format users data
    $formattedUsers = [];
    foreach ($users as $user) {
        $userData = formatUserData($user, true);
        $userData['stats'] = [
            'postsCount' => (int)$user['posts_count'],
            'followersCount' => (int)$user['followers_count'],
            'followingCount' => (int)$user['following_count'],
            'groupsCount' => (int)$user['groups_count'],
            'reportsCount' => (int)$user['reports_count']
        ];
        $userData['isBlocked'] = (bool)$user['is_blocked'];
        
        $formattedUsers[] = $userData;
    }
    
    sendJsonResponse([
        'users' => $formattedUsers,
        'pagination' => [
            'page' => $pagination['page'],
            'limit' => $pagination['limit'],
            'total' => (int)$totalCount,
            'hasMore' => ($pagination['offset'] + count($formattedUsers)) < $totalCount
        ]
    ]);
    
} catch (Exception $e) {
    logError('Admin get users error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
