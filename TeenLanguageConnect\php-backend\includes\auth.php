<?php
/**
 * Authentication and authorization functions
 */

/**
 * Register a new user
 */
function registerUser($userData) {
    $db = Database::getInstance();
    
    // Validate required fields
    $required = ['phoneNumber', 'countryCode', 'country', 'firstName', 'password'];
    foreach ($required as $field) {
        if (empty($userData[$field])) {
            throw new Exception("Field {$field} is required");
        }
    }
    
    // Validate phone number
    if (!validatePhoneNumber($userData['phoneNumber'])) {
        throw new Exception('Invalid phone number format');
    }
    
    // Validate email if provided
    if (!empty($userData['email']) && !validateEmail($userData['email'])) {
        throw new Exception('Invalid email format');
    }
    
    // Check if phone number already exists
    $existingUser = $db->fetchOne(
        "SELECT id FROM users WHERE phone_number = ?",
        [$userData['phoneNumber']]
    );
    
    if ($existingUser) {
        throw new Exception('Phone number already registered');
    }
    
    // Check if email already exists (if provided)
    if (!empty($userData['email'])) {
        $existingEmail = $db->fetchOne(
            "SELECT id FROM users WHERE email = ?",
            [$userData['email']]
        );
        
        if ($existingEmail) {
            throw new Exception('Email already registered');
        }
    }
    
    // Generate unique user ID and username
    $userId = generateUserId();
    $username = generateUsername($userData['firstName'], $userData['lastName'] ?? '');
    
    // Prepare user data
    $userRecord = [
        'id' => $userId,
        'phone_number' => $userData['phoneNumber'],
        'country_code' => $userData['countryCode'],
        'country' => $userData['country'],
        'first_name' => $userData['firstName'],
        'last_name' => $userData['lastName'] ?? null,
        'email' => $userData['email'] ?? null,
        'age' => $userData['age'] ?? null,
        'username' => $username,
        'bio' => $userData['bio'] ?? null,
        'status' => $userData['status'] ?? 'Student',
        'languages_spoken' => json_encode($userData['languagesSpoken'] ?? []),
        'languages_learning' => json_encode($userData['languagesLearning'] ?? []),
        'interests' => json_encode($userData['interests'] ?? []),
        'password_hash' => hashPassword($userData['password']),
        'profile_image_url' => $userData['profileImageUrl'] ?? null,
        'is_private' => $userData['isPrivate'] ?? false,
        'last_active' => date('Y-m-d H:i:s')
    ];
    
    $db->insert('users', $userRecord);
    
    return $userId;
}

/**
 * Authenticate user login
 */
function loginUser($phoneNumber, $password) {
    $db = Database::getInstance();
    
    // Find user by phone number
    $user = $db->fetchOne(
        "SELECT * FROM users WHERE phone_number = ? AND is_blocked = 0",
        [$phoneNumber]
    );
    
    if (!$user) {
        throw new Exception('Invalid phone number or password');
    }
    
    // Verify password
    if (!verifyPassword($password, $user['password_hash'])) {
        throw new Exception('Invalid phone number or password');
    }
    
    // Update last active
    $db->update('users', 
        ['last_active' => date('Y-m-d H:i:s')], 
        'id = ?', 
        [$user['id']]
    );
    
    // Set session
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['is_admin'] = (bool)$user['is_admin'];
    $_SESSION['login_time'] = time();
    
    return formatUserData($user, true);
}

/**
 * Logout user
 */
function logoutUser() {
    session_destroy();
    session_start();
}

/**
 * Get current authenticated user
 */
function getCurrentUser() {
    if (!isAuthenticated()) {
        return null;
    }
    
    $db = Database::getInstance();
    $user = $db->fetchOne(
        "SELECT * FROM users WHERE id = ? AND is_blocked = 0",
        [getCurrentUserId()]
    );
    
    return $user ? formatUserData($user, true) : null;
}

/**
 * Generate unique username
 */
function generateUsername($firstName, $lastName = '') {
    $db = Database::getInstance();
    
    $baseUsername = strtolower(trim($firstName . $lastName));
    $baseUsername = preg_replace('/[^a-z0-9]/', '', $baseUsername);
    
    if (strlen($baseUsername) < 3) {
        $baseUsername = 'user' . $baseUsername;
    }
    
    $username = $baseUsername;
    $counter = 1;
    
    // Check if username exists and generate unique one
    while (true) {
        $existing = $db->fetchOne(
            "SELECT id FROM users WHERE username = ?",
            [$username]
        );
        
        if (!$existing) {
            break;
        }
        
        $username = $baseUsername . $counter;
        $counter++;
    }
    
    return $username;
}

/**
 * Create admin user if not exists
 */
function createAdminUser() {
    $db = Database::getInstance();
    
    // Check if admin already exists
    $admin = $db->fetchOne(
        "SELECT id FROM users WHERE phone_number = ?",
        [ADMIN_PHONE]
    );
    
    if ($admin) {
        return; // Admin already exists
    }
    
    $adminData = [
        'id' => generateUserId(),
        'phone_number' => ADMIN_PHONE,
        'country_code' => '+1',
        'country' => 'United States',
        'first_name' => 'Super',
        'last_name' => 'Admin',
        'email' => ADMIN_EMAIL,
        'username' => ADMIN_USERNAME,
        'bio' => 'Platform Administrator',
        'status' => 'Admin',
        'languages_spoken' => json_encode(['English']),
        'languages_learning' => json_encode([]),
        'interests' => json_encode(['Platform Management']),
        'password_hash' => hashPassword(ADMIN_PASSWORD),
        'is_admin' => true,
        'last_active' => date('Y-m-d H:i:s')
    ];
    
    $db->insert('users', $adminData);
}

/**
 * Update user profile
 */
function updateUserProfile($userId, $updateData) {
    $db = Database::getInstance();
    
    // Validate user exists
    $user = $db->fetchOne("SELECT id FROM users WHERE id = ?", [$userId]);
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Prepare allowed update fields
    $allowedFields = [
        'first_name', 'last_name', 'email', 'bio', 'status',
        'languages_spoken', 'languages_learning', 'interests',
        'profile_image_url', 'is_private', 'country', 'age'
    ];
    
    $updateFields = [];
    foreach ($allowedFields as $field) {
        if (isset($updateData[$field])) {
            if (in_array($field, ['languages_spoken', 'languages_learning', 'interests'])) {
                $updateFields[$field] = json_encode($updateData[$field]);
            } else {
                $updateFields[$field] = $updateData[$field];
            }
        }
    }
    
    if (empty($updateFields)) {
        throw new Exception('No valid fields to update');
    }
    
    $updateFields['updated_at'] = date('Y-m-d H:i:s');
    
    $db->update('users', $updateFields, 'id = ?', [$userId]);
    
    return true;
}

/**
 * Change user password
 */
function changePassword($userId, $currentPassword, $newPassword) {
    $db = Database::getInstance();
    
    // Get current user
    $user = $db->fetchOne("SELECT password_hash FROM users WHERE id = ?", [$userId]);
    if (!$user) {
        throw new Exception('User not found');
    }
    
    // Verify current password
    if (!verifyPassword($currentPassword, $user['password_hash'])) {
        throw new Exception('Current password is incorrect');
    }
    
    // Update password
    $db->update('users', 
        ['password_hash' => hashPassword($newPassword)], 
        'id = ?', 
        [$userId]
    );
    
    return true;
}

/**
 * Block/unblock user (admin only)
 */
function toggleUserBlock($userId, $blocked = true) {
    $db = Database::getInstance();
    
    $db->update('users', 
        ['is_blocked' => $blocked], 
        'id = ?', 
        [$userId]
    );
    
    return true;
}

/**
 * Check session validity
 */
function validateSession() {
    if (!isAuthenticated()) {
        return false;
    }
    
    // Check session timeout
    $loginTime = $_SESSION['login_time'] ?? 0;
    if (time() - $loginTime > SESSION_LIFETIME) {
        logoutUser();
        return false;
    }
    
    return true;
}

// Create admin user on first load
try {
    createAdminUser();
} catch (Exception $e) {
    if (DEBUG_MODE) {
        logError("Failed to create admin user: " . $e->getMessage());
    }
}
?>
