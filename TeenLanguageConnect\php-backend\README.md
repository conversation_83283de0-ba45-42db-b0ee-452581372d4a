# TeenLanguageConnect PHP Backend

A complete PHP backend for the TeenLanguageConnect social media platform, converted from Node.js/TypeScript while maintaining all functionality.

## 🚀 Features

- **User Authentication**: Secure registration/login with phone numbers
- **Social Media Features**: Posts, stories, likes, comments, follows
- **Group Chat System**: Voice chat groups with participant/listener roles
- **Real-time Messaging**: WebSocket-based chat system
- **Admin Panel**: Complete administration interface
- **File Upload**: Secure image/video upload system
- **API-First Design**: RESTful APIs for all functionality

## 📋 Pre-built Admin Credentials

**Phone:** +1234567890  
**Password:** Admin123!@#  
**Email:** <EMAIL>  
**Username:** admin

## 🛠️ Installation

### Prerequisites
- PHP 7.4 or higher
- MySQL 5.7 or higher
- Apache/Nginx web server
- Composer (optional, for dependencies)

### Setup Steps

1. **Database Setup**
   ```bash
   mysql -u root -p < setup.sql
   ```

2. **Configure Database**
   Edit `config/config.php` and update database credentials:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'teen_language_connect');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

3. **Set Permissions**
   ```bash
   chmod 755 uploads/
   chmod 755 logs/
   chmod 644 config/config.php
   ```

4. **Start WebSocket Server** (for real-time chat)
   ```bash
   php websocket/server.php
   ```

5. **Access Admin Panel**
   Navigate to: `http://your-domain/php-backend/admin`

## 📁 Directory Structure

```
php-backend/
├── api/                    # API endpoints
│   ├── auth/              # Authentication APIs
│   ├── users/             # User management APIs
│   ├── posts/             # Posts and stories APIs
│   ├── groups/            # Group management APIs
│   ├── messages/          # Messaging APIs
│   ├── admin/             # Admin APIs
│   └── upload/            # File upload API
├── config/                # Configuration files
│   ├── config.php         # Main configuration
│   └── database.php       # Database connection
├── includes/              # Shared functions
│   ├── functions.php      # Utility functions
│   └── auth.php          # Authentication functions
├── admin/                 # Admin panel
│   └── index.php         # Admin dashboard
├── websocket/             # Real-time chat
│   ├── server.php        # WebSocket server
│   └── client.js         # Client-side WebSocket
├── uploads/               # File uploads
│   ├── profiles/         # Profile images
│   ├── posts/            # Post images/videos
│   └── messages/         # Message attachments
├── logs/                  # Error logs
├── setup.sql             # Database schema
├── .htaccess             # Apache configuration
└── index.php             # Main entry point
```

## 🔌 API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/user` - Get current user

### Users
- `GET /api/users/{id}` - Get user profile
- `POST /api/users/{id}/follow` - Follow user
- `POST /api/users/{id}/unfollow` - Unfollow user

### Posts
- `GET /api/posts/feed` - Get user feed
- `POST /api/posts` - Create post/story
- `GET /api/posts/{id}` - Get single post
- `POST /api/posts/{id}/like` - Like/unlike post

### Groups
- `GET /api/groups` - List groups
- `POST /api/groups` - Create group
- `GET /api/groups/{id}` - Get group details
- `POST /api/groups/{id}/join` - Join group

### Messages
- `GET /api/groups/{id}/messages` - Get group messages
- `POST /api/groups/{id}/messages` - Send message

### Admin (Requires admin privileges)
- `GET /api/admin/users` - List all users
- `GET /api/admin/stats` - Platform statistics

### File Upload
- `POST /api/upload` - Upload file

## 🔄 Real-time Features

The WebSocket server provides real-time functionality:

- **Group Chat**: Live messaging in groups
- **Typing Indicators**: Show when users are typing
- **User Presence**: Track online/offline status
- **Live Notifications**: Real-time updates

### WebSocket Events

**Client to Server:**
- `auth` - Authenticate user
- `join_group` - Join a group room
- `leave_group` - Leave a group room
- `group_message` - Send message to group
- `typing` - Send typing indicator

**Server to Client:**
- `auth_success` - Authentication successful
- `new_message` - New message received
- `user_joined` - User joined group
- `user_left` - User left group
- `user_typing` - User typing indicator

## 🔒 Security Features

- **Input Validation**: All inputs sanitized and validated
- **SQL Injection Protection**: Prepared statements used
- **File Upload Security**: Type and size validation
- **Rate Limiting**: Prevents abuse
- **Session Management**: Secure session handling
- **CORS Protection**: Proper CORS headers
- **Admin Authentication**: Separate admin privileges

## 📊 Admin Panel Features

- **User Management**: View, block/unblock users
- **Platform Statistics**: Real-time metrics
- **Content Moderation**: Monitor posts and messages
- **Activity Monitoring**: Track user activity
- **System Controls**: Emergency controls

## 🚀 Deployment

### Production Checklist

1. **Security**
   - Change default admin password
   - Update JWT secret key
   - Enable HTTPS
   - Set proper file permissions

2. **Performance**
   - Enable PHP OPcache
   - Configure MySQL optimization
   - Set up CDN for file uploads
   - Enable gzip compression

3. **Monitoring**
   - Set up error logging
   - Monitor WebSocket server
   - Database performance monitoring
   - User activity tracking

## 🔧 Configuration Options

Key configuration options in `config/config.php`:

```php
// Database
define('DB_HOST', 'localhost');
define('DB_NAME', 'teen_language_connect');

// Security
define('JWT_SECRET', 'your-secret-key');
define('SESSION_LIFETIME', 86400);

// File Uploads
define('UPLOAD_MAX_SIZE', 10 * 1024 * 1024); // 10MB
define('UPLOAD_ALLOWED_TYPES', ['jpg', 'jpeg', 'png', 'gif', 'mp4']);

// WebSocket
define('WEBSOCKET_HOST', 'localhost');
define('WEBSOCKET_PORT', 8080);

// Admin Credentials
define('ADMIN_USERNAME', 'admin');
define('ADMIN_PASSWORD', 'Admin123!@#');
```

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   - Check database credentials in config.php
   - Ensure MySQL service is running
   - Verify database exists

2. **File Upload Errors**
   - Check upload directory permissions
   - Verify PHP upload settings
   - Check file size limits

3. **WebSocket Connection Failed**
   - Ensure WebSocket server is running
   - Check firewall settings
   - Verify port availability

4. **Admin Panel Access Denied**
   - Use correct admin credentials
   - Check session configuration
   - Verify admin user exists in database

## 📝 License

This project is licensed under the MIT License.

## 🤝 Support

For support and questions:
- Check the troubleshooting section
- Review error logs in `/logs/`
- Verify configuration settings
- Test API endpoints individually
