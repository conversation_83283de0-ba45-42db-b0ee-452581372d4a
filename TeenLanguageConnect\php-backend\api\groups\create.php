<?php
/**
 * Create Group API
 */

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $currentUserId = getCurrentUserId();
    
    // Get JSON input
    $input = getJsonInput();
    
    // Validate required fields
    if (empty($input['name'])) {
        sendErrorResponse('Group name is required');
    }
    
    if (empty($input['language'])) {
        sendErrorResponse('Group language is required');
    }
    
    $db = Database::getInstance();
    
    // Check if user already has too many groups as admin
    $userGroupsCount = $db->fetchOne(
        "SELECT COUNT(*) as count FROM groups WHERE admin_id = ? AND is_active = 1",
        [$currentUserId]
    )['count'];
    
    if ($userGroupsCount >= 5) { // Limit to 5 groups per user
        sendErrorResponse('You can only create up to 5 active groups');
    }
    
    // Prepare group data
    $groupData = [
        'name' => $input['name'],
        'description' => $input['description'] ?? null,
        'language' => $input['language'],
        'admin_id' => $currentUserId,
        'max_participants' => min($input['maxParticipants'] ?? MAX_GROUP_PARTICIPANTS, MAX_GROUP_PARTICIPANTS),
        'is_private' => $input['isPrivate'] ?? false,
        'current_participants' => 1 // Admin is automatically a participant
    ];
    
    // Create group
    $groupId = $db->insert('groups', $groupData);
    
    // Add admin as a member
    $db->insert('group_members', [
        'group_id' => $groupId,
        'user_id' => $currentUserId,
        'role' => 'admin'
    ]);
    
    // Get the created group with admin data
    $group = $db->fetchOne("
        SELECT 
            g.*,
            u.username as admin_username,
            u.first_name as admin_first_name,
            u.last_name as admin_last_name,
            u.profile_image_url as admin_profile_image
        FROM groups g
        JOIN users u ON g.admin_id = u.id
        WHERE g.id = ?
    ", [$groupId]);
    
    $adminData = [
        'id' => $group['admin_id'],
        'username' => $group['admin_username'],
        'firstName' => $group['admin_first_name'],
        'lastName' => $group['admin_last_name'],
        'profileImageUrl' => $group['admin_profile_image']
    ];
    
    $formattedGroup = [
        'id' => $group['id'],
        'name' => $group['name'],
        'description' => $group['description'],
        'language' => $group['language'],
        'adminId' => $group['admin_id'],
        'maxParticipants' => $group['max_participants'],
        'currentParticipants' => $group['current_participants'],
        'isActive' => (bool)$group['is_active'],
        'isPrivate' => (bool)$group['is_private'],
        'createdAt' => $group['created_at'],
        'admin' => $adminData,
        'userRole' => 'admin',
        'isFull' => false
    ];
    
    sendJsonResponse([
        'message' => 'Group created successfully',
        'group' => $formattedGroup
    ], 201);
    
} catch (Exception $e) {
    logError('Create group error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
