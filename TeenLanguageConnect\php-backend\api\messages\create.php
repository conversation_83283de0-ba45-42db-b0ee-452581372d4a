<?php
/**
 * Create Message API
 */

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $groupId = $_POST['group_id'] ?? null;
    $currentUserId = getCurrentUserId();
    
    if (!$groupId) {
        sendErrorResponse('Group ID is required');
    }
    
    // Get JSON input
    $input = getJsonInput();
    
    // Validate required fields
    if (empty($input['content'])) {
        sendErrorResponse('Message content is required');
    }
    
    $db = Database::getInstance();
    
    // Check if user is a member of the group
    $membership = $db->fetchOne(
        "SELECT role FROM group_members WHERE group_id = ? AND user_id = ?",
        [$groupId, $currentUserId]
    );
    
    if (!$membership) {
        sendErrorResponse('You are not a member of this group', 403);
    }
    
    // Check if group is active
    $group = $db->fetchOne(
        "SELECT is_active FROM groups WHERE id = ?",
        [$groupId]
    );
    
    if (!$group || !$group['is_active']) {
        sendErrorResponse('Group not found or inactive', 404);
    }
    
    // Prepare message data
    $messageData = [
        'group_id' => $groupId,
        'user_id' => $currentUserId,
        'content' => $input['content'],
        'message_type' => $input['messageType'] ?? 'text',
        'is_one_time' => $input['isOneTime'] ?? false
    ];
    
    // Create message
    $messageId = $db->insert('messages', $messageData);
    
    // Get the created message with user data
    $message = $db->fetchOne("
        SELECT 
            m.*,
            u.username,
            u.first_name,
            u.last_name,
            u.profile_image_url
        FROM messages m
        JOIN users u ON m.user_id = u.id
        WHERE m.id = ?
    ", [$messageId]);
    
    $messageUser = [
        'id' => $message['user_id'],
        'username' => $message['username'],
        'firstName' => $message['first_name'],
        'lastName' => $message['last_name'],
        'profileImageUrl' => $message['profile_image_url']
    ];
    
    $formattedMessage = [
        'id' => $message['id'],
        'groupId' => $message['group_id'],
        'userId' => $message['user_id'],
        'content' => $message['content'],
        'messageType' => $message['message_type'],
        'isOneTime' => (bool)$message['is_one_time'],
        'createdAt' => $message['created_at'],
        'user' => $messageUser
    ];
    
    sendJsonResponse([
        'message' => 'Message sent successfully',
        'data' => $formattedMessage
    ], 201);
    
} catch (Exception $e) {
    logError('Create message error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
