import { useEffect } from "react";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import TopBar from "@/components/layout/top-bar";
import BottomNav from "@/components/layout/bottom-nav";
import Stories from "@/components/feed/stories";
import Post from "@/components/feed/post";
import RegistrationModal from "@/components/modals/registration-modal";
import CreatePostModal from "@/components/modals/create-post-modal";
import { useQuery } from "@tanstack/react-query";
import { Plus } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState } from "react";

export default function Home() {
  const { user, isAuthenticated } = useAuth();
  const { toast } = useToast();
  const [showRegistration, setShowRegistration] = useState(false);
  const [showCreatePost, setShowCreatePost] = useState(false);

  // Check if user needs to complete registration
  useEffect(() => {
    if (user && (!user.phoneNumber || !user.username)) {
      setShowRegistration(true);
    }
  }, [user]);

  const { data: feedData, isLoading: feedLoading } = useQuery({
    queryKey: ["posts", "feed"],
    queryFn: () => import("@/lib/authUtils").then(m => m.getFeed()),
    enabled: isAuthenticated && !!user?.phoneNumber,
  });

  const { data: storiesData, isLoading: storiesLoading } = useQuery({
    queryKey: ["posts", "stories"],
    queryFn: () => import("@/lib/authUtils").then(m => m.getFeed(1, 20)).then(data =>
      data.posts?.filter((post: any) => post.isStory) || []
    ),
    enabled: isAuthenticated && !!user?.phoneNumber,
  });

  const feedPosts = feedData?.posts || [];
  const stories = storiesData || [];

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="mobile-container">
      <TopBar />
      
      <main className="pb-20">
        {/* Stories Section */}
        <section className="px-4 py-3 border-b border-border">
          {storiesLoading ? (
            <div className="flex space-x-4 overflow-x-auto scrollbar-hide">
              {[...Array(5)].map((_, i) => (
                <div key={i} className="flex flex-col items-center space-y-1 flex-shrink-0">
                  <div className="w-16 h-16 rounded-full bg-muted animate-pulse"></div>
                  <div className="w-12 h-3 bg-muted rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          ) : (
            <Stories stories={stories || []} />
          )}
        </section>

        {/* Feed Section */}
        <section className="space-y-0">
          {feedLoading ? (
            <div className="space-y-0">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="bg-card border-b border-border p-4">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 rounded-full bg-muted animate-pulse"></div>
                    <div className="space-y-2">
                      <div className="w-24 h-3 bg-muted rounded animate-pulse"></div>
                      <div className="w-16 h-2 bg-muted rounded animate-pulse"></div>
                    </div>
                  </div>
                  <div className="w-full h-80 bg-muted rounded animate-pulse"></div>
                </div>
              ))}
            </div>
          ) : feedPosts && feedPosts.length > 0 ? (
            feedPosts.map((post: any) => (
              <Post key={post.id} post={post} />
            ))
          ) : (
            <div className="p-8 text-center">
              <div className="w-16 h-16 bg-muted rounded-full mx-auto mb-4 flex items-center justify-center">
                <Plus className="w-8 h-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold text-card-foreground mb-2">
                No posts yet
              </h3>
              <p className="text-muted-foreground mb-4">
                Follow other users or create your first post to see content here
              </p>
              <Button 
                onClick={() => setShowCreatePost(true)}
                className="gradient-primary text-white"
              >
                Create First Post
              </Button>
            </div>
          )}
        </section>
      </main>

      <BottomNav />

      {/* Floating Action Button */}
      <Button
        onClick={() => setShowCreatePost(true)}
        className="fixed bottom-24 right-4 w-14 h-14 gradient-primary text-white rounded-full shadow-lg btn-hover-scale z-30"
      >
        <Plus className="w-6 h-6" />
      </Button>

      {/* Modals */}
      <RegistrationModal 
        open={showRegistration} 
        onOpenChange={setShowRegistration}
      />
      <CreatePostModal 
        open={showCreatePost} 
        onOpenChange={setShowCreatePost}
      />
    </div>
  );
}
