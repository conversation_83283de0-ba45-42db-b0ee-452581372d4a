<?php
/**
 * User Logout API
 */

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    // Logout user
    logoutUser();
    
    sendJsonResponse([
        'message' => 'Logout successful'
    ]);
    
} catch (Exception $e) {
    logError('Logout error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
