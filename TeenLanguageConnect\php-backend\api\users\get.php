<?php
/**
 * Get User Profile API
 */

try {
    // Only allow GET requests
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $userId = $_GET['user_id'] ?? null;
    $currentUserId = getCurrentUserId();
    
    if (!$userId) {
        sendErrorResponse('User ID is required');
    }
    
    $db = Database::getInstance();
    
    // Get user data
    $user = $db->fetchOne(
        "SELECT * FROM users WHERE id = ? AND is_blocked = 0",
        [$userId]
    );
    
    if (!$user) {
        sendErrorResponse('User not found', 404);
    }
    
    // Check if current user is blocked by this user
    $isBlocked = $db->fetchOne(
        "SELECT id FROM blocked_users WHERE blocker_id = ? AND blocked_id = ?",
        [$userId, $currentUserId]
    );
    
    if ($isBlocked) {
        sendErrorResponse('User not found', 404);
    }
    
    // Get additional user stats
    $stats = $db->fetchOne("
        SELECT 
            (SELECT COUNT(*) FROM posts WHERE user_id = ? AND is_story = 0) as posts_count,
            (SELECT COUNT(*) FROM follows WHERE following_id = ?) as followers_count,
            (SELECT COUNT(*) FROM follows WHERE follower_id = ?) as following_count
    ", [$userId, $userId, $userId]);
    
    // Check if current user follows this user
    $isFollowing = false;
    if ($currentUserId !== $userId) {
        $followRecord = $db->fetchOne(
            "SELECT id FROM follows WHERE follower_id = ? AND following_id = ?",
            [$currentUserId, $userId]
        );
        $isFollowing = (bool)$followRecord;
    }
    
    // Format user data
    $includePrivate = ($currentUserId === $userId);
    $userData = formatUserData($user, $includePrivate);
    
    // Add stats and relationship info
    $userData['postsCount'] = (int)$stats['posts_count'];
    $userData['followersCount'] = (int)$stats['followers_count'];
    $userData['followingCount'] = (int)$stats['following_count'];
    $userData['isFollowing'] = $isFollowing;
    
    sendJsonResponse($userData);
    
} catch (Exception $e) {
    logError('Get user error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
