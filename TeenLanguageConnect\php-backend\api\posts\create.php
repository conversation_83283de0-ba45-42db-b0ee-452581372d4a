<?php
/**
 * Create Post API
 */

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $currentUserId = getCurrentUserId();
    
    // Get JSON input
    $input = getJsonInput();
    
    // Validate input
    if (empty($input['content']) && empty($input['imageUrl']) && empty($input['videoUrl'])) {
        sendErrorResponse('Post must have content, image, or video');
    }
    
    $db = Database::getInstance();
    
    // Prepare post data
    $postData = [
        'user_id' => $currentUserId,
        'content' => $input['content'] ?? null,
        'image_url' => $input['imageUrl'] ?? null,
        'video_url' => $input['videoUrl'] ?? null,
        'is_story' => $input['isStory'] ?? false
    ];
    
    // Set story expiration if it's a story
    if ($postData['is_story']) {
        $postData['story_expires_at'] = calculateStoryExpiration();
    }
    
    // Create post
    $postId = $db->insert('posts', $postData);
    
    // Get the created post with user data
    $post = $db->fetchOne("
        SELECT 
            p.*,
            u.username,
            u.first_name,
            u.last_name,
            u.profile_image_url,
            u.country
        FROM posts p
        JOIN users u ON p.user_id = u.id
        WHERE p.id = ?
    ", [$postId]);
    
    $userData = [
        'id' => $post['user_id'],
        'username' => $post['username'],
        'first_name' => $post['first_name'],
        'last_name' => $post['last_name'],
        'profile_image_url' => $post['profile_image_url'],
        'country' => $post['country']
    ];
    
    $formattedPost = formatPostData($post, $userData);
    $formattedPost['userLiked'] = false;
    
    sendJsonResponse([
        'message' => 'Post created successfully',
        'post' => $formattedPost
    ], 201);
    
} catch (Exception $e) {
    logError('Create post error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
