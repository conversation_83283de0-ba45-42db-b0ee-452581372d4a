<?php
/**
 * File Upload API
 */

try {
    // Only allow POST requests
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $currentUserId = getCurrentUserId();
    
    // Check if file was uploaded
    if (!isset($_FILES['file'])) {
        sendErrorResponse('No file uploaded');
    }
    
    $file = $_FILES['file'];
    $uploadType = $_POST['type'] ?? 'general'; // profile, post, message, general
    
    // Validate upload type
    $allowedTypes = ['profile', 'post', 'message', 'general'];
    if (!in_array($uploadType, $allowedTypes)) {
        sendErrorResponse('Invalid upload type');
    }
    
    // Rate limiting for uploads
    $clientIp = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    checkRateLimit('upload_' . $currentUserId, 20, 3600); // 20 uploads per hour per user
    
    try {
        // Upload file
        $filePath = uploadFile($file, $uploadType);
        
        // Log upload activity
        $db = Database::getInstance();
        $db->query("
            INSERT INTO notifications (user_id, type, title, message, related_id) 
            VALUES (?, 'admin', 'File Upload', ?, ?)
        ", [
            $currentUserId,
            "File uploaded: {$file['name']} ({$uploadType})",
            $filePath
        ]);
        
        sendJsonResponse([
            'message' => 'File uploaded successfully',
            'url' => $filePath,
            'type' => $uploadType,
            'originalName' => $file['name']
        ], 201);
        
    } catch (Exception $e) {
        logError('File upload error: ' . $e->getMessage(), [
            'user_id' => $currentUserId,
            'file_name' => $file['name'] ?? 'unknown',
            'file_size' => $file['size'] ?? 0,
            'upload_type' => $uploadType
        ]);
        sendErrorResponse($e->getMessage());
    }
    
} catch (Exception $e) {
    logError('Upload API error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
