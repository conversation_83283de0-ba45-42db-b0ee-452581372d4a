/**
 * WebSocket Client for Real-time Chat
 * Include this in your frontend to connect to the PHP WebSocket server
 */

class ChatWebSocket {
    constructor(url = 'ws://localhost:8080') {
        this.url = url;
        this.socket = null;
        this.isConnected = false;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 1000;
        this.userId = null;
        this.sessionId = null;
        this.currentGroup = null;
        this.eventHandlers = {};
        
        // Bind methods
        this.connect = this.connect.bind(this);
        this.disconnect = this.disconnect.bind(this);
        this.send = this.send.bind(this);
        this.onMessage = this.onMessage.bind(this);
        this.onOpen = this.onOpen.bind(this);
        this.onClose = this.onClose.bind(this);
        this.onError = this.onError.bind(this);
    }
    
    connect(userId, sessionId) {
        this.userId = userId;
        this.sessionId = sessionId;
        
        try {
            this.socket = new WebSocket(this.url);
            this.socket.onopen = this.onOpen;
            this.socket.onmessage = this.onMessage;
            this.socket.onclose = this.onClose;
            this.socket.onerror = this.onError;
        } catch (error) {
            console.error('WebSocket connection error:', error);
            this.scheduleReconnect();
        }
    }
    
    disconnect() {
        this.isConnected = false;
        if (this.socket) {
            this.socket.close();
            this.socket = null;
        }
    }
    
    onOpen() {
        console.log('WebSocket connected');
        this.isConnected = true;
        this.reconnectAttempts = 0;
        
        // Authenticate
        this.send({
            type: 'auth',
            userId: this.userId,
            sessionId: this.sessionId
        });
        
        this.emit('connected');
    }
    
    onMessage(event) {
        try {
            const data = JSON.parse(event.data);
            this.handleMessage(data);
        } catch (error) {
            console.error('Error parsing WebSocket message:', error);
        }
    }
    
    onClose() {
        console.log('WebSocket disconnected');
        this.isConnected = false;
        this.emit('disconnected');
        
        if (this.reconnectAttempts < this.maxReconnectAttempts) {
            this.scheduleReconnect();
        }
    }
    
    onError(error) {
        console.error('WebSocket error:', error);
        this.emit('error', error);
    }
    
    scheduleReconnect() {
        this.reconnectAttempts++;
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
        
        console.log(`Reconnecting in ${delay}ms (attempt ${this.reconnectAttempts})`);
        
        setTimeout(() => {
            if (this.userId && this.sessionId) {
                this.connect(this.userId, this.sessionId);
            }
        }, delay);
    }
    
    send(data) {
        if (this.socket && this.isConnected) {
            this.socket.send(JSON.stringify(data));
        } else {
            console.warn('WebSocket not connected, message not sent:', data);
        }
    }
    
    handleMessage(data) {
        switch (data.type) {
            case 'auth_success':
                console.log('WebSocket authentication successful');
                this.emit('authenticated');
                break;
                
            case 'auth_error':
                console.error('WebSocket authentication failed:', data.message);
                this.emit('auth_error', data.message);
                break;
                
            case 'joined_group':
                this.currentGroup = data.groupId;
                this.emit('joined_group', data.groupId);
                break;
                
            case 'left_group':
                if (this.currentGroup === data.groupId) {
                    this.currentGroup = null;
                }
                this.emit('left_group', data.groupId);
                break;
                
            case 'new_message':
                this.emit('message', data);
                break;
                
            case 'user_joined':
                this.emit('user_joined', data);
                break;
                
            case 'user_left':
                this.emit('user_left', data);
                break;
                
            case 'user_typing':
                this.emit('user_typing', data);
                break;
                
            case 'pong':
                // Handle ping/pong for connection health
                break;
                
            case 'error':
                console.error('WebSocket server error:', data.message);
                this.emit('server_error', data.message);
                break;
                
            default:
                console.log('Unknown message type:', data.type);
        }
    }
    
    // Group management methods
    joinGroup(groupId) {
        this.send({
            type: 'join_group',
            groupId: groupId
        });
    }
    
    leaveGroup(groupId) {
        this.send({
            type: 'leave_group',
            groupId: groupId
        });
    }
    
    sendMessage(groupId, content, messageType = 'text') {
        this.send({
            type: 'group_message',
            groupId: groupId,
            content: content,
            messageType: messageType
        });
    }
    
    sendTyping(groupId, isTyping = true) {
        this.send({
            type: 'typing',
            groupId: groupId,
            isTyping: isTyping
        });
    }
    
    // Connection health
    ping() {
        this.send({ type: 'ping' });
    }
    
    // Event handling
    on(event, handler) {
        if (!this.eventHandlers[event]) {
            this.eventHandlers[event] = [];
        }
        this.eventHandlers[event].push(handler);
    }
    
    off(event, handler) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event] = this.eventHandlers[event].filter(h => h !== handler);
        }
    }
    
    emit(event, ...args) {
        if (this.eventHandlers[event]) {
            this.eventHandlers[event].forEach(handler => {
                try {
                    handler(...args);
                } catch (error) {
                    console.error('Error in event handler:', error);
                }
            });
        }
    }
    
    // Utility methods
    isConnectedToGroup(groupId) {
        return this.currentGroup === groupId;
    }
    
    getConnectionStatus() {
        return {
            connected: this.isConnected,
            currentGroup: this.currentGroup,
            reconnectAttempts: this.reconnectAttempts
        };
    }
}

// Export for use in modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ChatWebSocket;
}

// Example usage:
/*
const chat = new ChatWebSocket('ws://localhost:8080');

// Set up event handlers
chat.on('connected', () => {
    console.log('Connected to chat server');
});

chat.on('message', (message) => {
    console.log('New message:', message);
    // Update UI with new message
});

chat.on('user_joined', (data) => {
    console.log('User joined group:', data.userId);
    // Update UI to show user joined
});

chat.on('user_typing', (data) => {
    console.log('User typing:', data.userId);
    // Show typing indicator
});

// Connect with user credentials
chat.connect('user123', 'session456');

// Join a group
chat.joinGroup(1);

// Send a message
chat.sendMessage(1, 'Hello everyone!');

// Send typing indicator
chat.sendTyping(1, true);
*/
