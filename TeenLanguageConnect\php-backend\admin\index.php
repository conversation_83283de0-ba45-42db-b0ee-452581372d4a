<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TeenLanguageConnect - Admin Panel</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .header h1 {
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
        }
        
        .admin-sections {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .section {
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        
        .section h3 {
            color: #667eea;
            margin-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a6fd8;
        }
        
        .btn-danger {
            background: #e74c3c;
        }
        
        .btn-danger:hover {
            background: #c0392b;
        }
        
        .user-list, .activity-list {
            max-height: 300px;
            overflow-y: auto;
        }
        
        .user-item, .activity-item {
            padding: 10px;
            border-bottom: 1px solid #f0f0f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .user-info {
            flex: 1;
        }
        
        .user-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .user-details {
            font-size: 0.8em;
            color: #666;
        }
        
        .activity-time {
            font-size: 0.8em;
            color: #666;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .error {
            background: #e74c3c;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .success {
            background: #27ae60;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .admin-credentials {
            background: #f8f9fa;
            border: 2px solid #667eea;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .credentials-title {
            color: #667eea;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .credential-item {
            margin: 5px 0;
            font-family: monospace;
            background: white;
            padding: 5px 10px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌍 TeenLanguageConnect Admin Panel</h1>
            <p>Platform administration and monitoring dashboard</p>
        </div>
        
        <div class="admin-credentials">
            <div class="credentials-title">🔑 Pre-built Admin Credentials:</div>
            <div class="credential-item"><strong>Phone:</strong> +1234567890</div>
            <div class="credential-item"><strong>Password:</strong> Admin123!@#</div>
            <div class="credential-item"><strong>Email:</strong> <EMAIL></div>
            <div class="credential-item"><strong>Username:</strong> admin</div>
        </div>
        
        <div class="stats-grid" id="statsGrid">
            <div class="stat-card">
                <div class="stat-number" id="totalUsers">-</div>
                <div class="stat-label">Total Users</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeUsers">-</div>
                <div class="stat-label">Active Today</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalPosts">-</div>
                <div class="stat-label">Total Posts</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="activeGroups">-</div>
                <div class="stat-label">Active Groups</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="pendingReports">-</div>
                <div class="stat-label">Pending Reports</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="messagesToday">-</div>
                <div class="stat-label">Messages Today</div>
            </div>
        </div>
        
        <div class="admin-sections">
            <div class="section">
                <h3>👥 User Management</h3>
                <div class="user-list" id="userList">
                    <div class="loading">Loading users...</div>
                </div>
                <button class="btn" onclick="loadUsers()">Refresh Users</button>
                <button class="btn" onclick="exportUsers()">Export Users</button>
            </div>
            
            <div class="section">
                <h3>📊 Platform Statistics</h3>
                <div id="languageStats"></div>
                <div id="countryStats"></div>
                <button class="btn" onclick="loadStats()">Refresh Stats</button>
            </div>
            
            <div class="section">
                <h3>🔄 Recent Activity</h3>
                <div class="activity-list" id="activityList">
                    <div class="loading">Loading activity...</div>
                </div>
                <button class="btn" onclick="loadActivity()">Refresh Activity</button>
            </div>
            
            <div class="section">
                <h3>⚙️ System Controls</h3>
                <button class="btn" onclick="cleanupExpiredStories()">Cleanup Expired Stories</button>
                <button class="btn" onclick="generateReport()">Generate Report</button>
                <button class="btn btn-danger" onclick="emergencyShutdown()">Emergency Shutdown</button>
                <div id="systemMessages"></div>
            </div>
        </div>
    </div>

    <script>
        // Admin panel JavaScript
        const API_BASE = '../api';
        
        // Load initial data
        document.addEventListener('DOMContentLoaded', function() {
            loadStats();
            loadUsers();
            loadActivity();
        });
        
        async function apiCall(endpoint, options = {}) {
            try {
                const response = await fetch(`${API_BASE}${endpoint}`, {
                    credentials: 'include',
                    ...options
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                return await response.json();
            } catch (error) {
                console.error('API call failed:', error);
                showMessage('API call failed: ' + error.message, 'error');
                throw error;
            }
        }
        
        async function loadStats() {
            try {
                const data = await apiCall('/admin/stats');
                
                // Update overview stats
                document.getElementById('totalUsers').textContent = data.overview.totalUsers;
                document.getElementById('activeUsers').textContent = data.overview.activeUsersToday;
                document.getElementById('totalPosts').textContent = data.overview.totalPosts;
                document.getElementById('activeGroups').textContent = data.overview.activeGroups;
                document.getElementById('pendingReports').textContent = data.overview.pendingReports;
                document.getElementById('messagesToday').textContent = data.overview.messagesToday;
                
                // Update language stats
                const languageStats = document.getElementById('languageStats');
                languageStats.innerHTML = '<h4>Top Languages</h4>';
                data.topLanguages.forEach(lang => {
                    languageStats.innerHTML += `<div>${lang.language}: ${lang.groupCount} groups</div>`;
                });
                
                // Update country stats
                const countryStats = document.getElementById('countryStats');
                countryStats.innerHTML = '<h4>Top Countries</h4>';
                data.topCountries.forEach(country => {
                    countryStats.innerHTML += `<div>${country.country}: ${country.userCount} users</div>`;
                });
                
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }
        
        async function loadUsers() {
            try {
                const data = await apiCall('/admin/users?limit=10');
                const userList = document.getElementById('userList');
                
                userList.innerHTML = '';
                data.users.forEach(user => {
                    const userItem = document.createElement('div');
                    userItem.className = 'user-item';
                    userItem.innerHTML = `
                        <div class="user-info">
                            <div class="user-name">${user.firstName} ${user.lastName} (@${user.username})</div>
                            <div class="user-details">
                                ${user.email} | ${user.phoneNumber} | ${user.country}
                                <br>Posts: ${user.stats.postsCount} | Followers: ${user.stats.followersCount}
                            </div>
                        </div>
                        <div>
                            ${user.isBlocked ? 
                                '<button class="btn" onclick="unblockUser(\'' + user.id + '\')">Unblock</button>' :
                                '<button class="btn btn-danger" onclick="blockUser(\'' + user.id + '\')">Block</button>'
                            }
                        </div>
                    `;
                    userList.appendChild(userItem);
                });
                
            } catch (error) {
                document.getElementById('userList').innerHTML = '<div class="error">Failed to load users</div>';
            }
        }
        
        async function loadActivity() {
            try {
                const data = await apiCall('/admin/stats');
                const activityList = document.getElementById('activityList');
                
                activityList.innerHTML = '';
                data.recentActivity.forEach(activity => {
                    const activityItem = document.createElement('div');
                    activityItem.className = 'activity-item';
                    activityItem.innerHTML = `
                        <div>${activity.description}</div>
                        <div class="activity-time">${new Date(activity.timestamp).toLocaleString()}</div>
                    `;
                    activityList.appendChild(activityItem);
                });
                
            } catch (error) {
                document.getElementById('activityList').innerHTML = '<div class="error">Failed to load activity</div>';
            }
        }
        
        async function blockUser(userId) {
            if (confirm('Are you sure you want to block this user?')) {
                try {
                    await apiCall(`/admin/users/${userId}/block`, { method: 'POST' });
                    showMessage('User blocked successfully', 'success');
                    loadUsers();
                } catch (error) {
                    showMessage('Failed to block user', 'error');
                }
            }
        }
        
        async function unblockUser(userId) {
            try {
                await apiCall(`/admin/users/${userId}/unblock`, { method: 'POST' });
                showMessage('User unblocked successfully', 'success');
                loadUsers();
            } catch (error) {
                showMessage('Failed to unblock user', 'error');
            }
        }
        
        function showMessage(message, type) {
            const systemMessages = document.getElementById('systemMessages');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            systemMessages.appendChild(messageDiv);
            
            setTimeout(() => {
                messageDiv.remove();
            }, 5000);
        }
        
        function cleanupExpiredStories() {
            showMessage('Cleaning up expired stories...', 'success');
            // This would call a cleanup endpoint
        }
        
        function generateReport() {
            showMessage('Generating platform report...', 'success');
            // This would generate and download a report
        }
        
        function emergencyShutdown() {
            if (confirm('Are you sure you want to initiate emergency shutdown? This will disable new registrations and posts.')) {
                showMessage('Emergency shutdown initiated', 'error');
                // This would call an emergency shutdown endpoint
            }
        }
        
        function exportUsers() {
            showMessage('Exporting user data...', 'success');
            // This would export user data to CSV
        }
    </script>
</body>
</html>
