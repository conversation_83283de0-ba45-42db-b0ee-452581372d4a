<?php
/**
 * Get Groups List API
 */

try {
    // Only allow GET requests
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $currentUserId = getCurrentUserId();
    $pagination = paginate($_GET['page'] ?? 1, $_GET['limit'] ?? 20);
    $language = $_GET['language'] ?? null;
    $search = $_GET['search'] ?? null;
    
    $db = Database::getInstance();
    
    // Build query conditions
    $conditions = ['g.is_active = 1'];
    $params = [];
    
    if ($language) {
        $conditions[] = 'g.language = ?';
        $params[] = $language;
    }
    
    if ($search) {
        $conditions[] = '(g.name LIKE ? OR g.description LIKE ?)';
        $params[] = "%{$search}%";
        $params[] = "%{$search}%";
    }
    
    $whereClause = implode(' AND ', $conditions);
    
    // Get groups with admin info and user membership status
    $groups = $db->fetchAll("
        SELECT 
            g.*,
            u.username as admin_username,
            u.first_name as admin_first_name,
            u.last_name as admin_last_name,
            u.profile_image_url as admin_profile_image,
            (SELECT role FROM group_members gm WHERE gm.group_id = g.id AND gm.user_id = ?) as user_role
        FROM groups g
        JOIN users u ON g.admin_id = u.id
        WHERE {$whereClause}
        ORDER BY g.created_at DESC
        LIMIT ? OFFSET ?
    ", array_merge([$currentUserId], $params, [$pagination['limit'], $pagination['offset']]));
    
    // Format groups data
    $formattedGroups = [];
    foreach ($groups as $group) {
        $adminData = [
            'id' => $group['admin_id'],
            'username' => $group['admin_username'],
            'firstName' => $group['admin_first_name'],
            'lastName' => $group['admin_last_name'],
            'profileImageUrl' => $group['admin_profile_image']
        ];
        
        $formattedGroups[] = [
            'id' => $group['id'],
            'name' => $group['name'],
            'description' => $group['description'],
            'language' => $group['language'],
            'adminId' => $group['admin_id'],
            'maxParticipants' => $group['max_participants'],
            'currentParticipants' => $group['current_participants'],
            'isActive' => (bool)$group['is_active'],
            'isPrivate' => (bool)$group['is_private'],
            'createdAt' => $group['created_at'],
            'admin' => $adminData,
            'userRole' => $group['user_role'],
            'isFull' => $group['current_participants'] >= $group['max_participants']
        ];
    }
    
    sendJsonResponse([
        'groups' => $formattedGroups,
        'pagination' => [
            'page' => $pagination['page'],
            'limit' => $pagination['limit'],
            'hasMore' => count($formattedGroups) === $pagination['limit']
        ]
    ]);
    
} catch (Exception $e) {
    logError('Get groups error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
