@echo off
echo Starting TeenLanguageConnect PHP Backend...
echo.

REM Check if <PERSON><PERSON> is installed
php --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: PHP is not installed or not in PATH
    echo Please install PHP 7.4 or higher and add it to your PATH
    pause
    exit /b 1
)

REM Check if MySQL is running
echo Checking MySQL connection...
mysql --version >nul 2>&1
if %errorlevel% neq 0 (
    echo WARNING: MySQL command not found. Make sure MySQL is installed and running.
    echo You can continue if MySQL is running on a different setup.
    echo.
)

REM Create database if it doesn't exist
echo Setting up database...
mysql -u root -p -e "CREATE DATABASE IF NOT EXISTS teen_language_connect CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>nul
if %errorlevel% neq 0 (
    echo Note: Could not create database automatically. Please create it manually:
    echo   CREATE DATABASE teen_language_connect CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
    echo.
)

REM Import database schema
echo Importing database schema...
mysql -u root -p teen_language_connect < php-backend/setup.sql 2>nul
if %errorlevel% neq 0 (
    echo Note: Could not import schema automatically. Please run:
    echo   mysql -u root -p teen_language_connect < php-backend/setup.sql
    echo.
)

REM Start PHP development server
echo.
echo Starting PHP development server on http://localhost:8000
echo Admin panel will be available at: http://localhost:8000/php-backend/admin
echo.
echo Pre-built Admin Credentials:
echo   Phone: +1234567890
echo   Password: Admin123!@#
echo   Email: <EMAIL>
echo.
echo Starting WebSocket server in background...
start /b php php-backend/websocket/server.php

echo.
echo Starting PHP server...
cd php-backend
php -S localhost:8000

pause
