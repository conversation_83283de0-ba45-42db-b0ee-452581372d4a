<?php
/**
 * Get Single Post API
 */

try {
    // Only allow GET requests
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        sendErrorResponse('Method not allowed', 405);
    }
    
    // Require authentication
    requireAuth();
    
    $postId = $_GET['post_id'] ?? null;
    $currentUserId = getCurrentUserId();
    
    if (!$postId) {
        sendErrorResponse('Post ID is required');
    }
    
    $db = Database::getInstance();
    
    // Get post with user data
    $post = $db->fetchOne("
        SELECT 
            p.*,
            u.username,
            u.first_name,
            u.last_name,
            u.profile_image_url,
            u.country,
            (SELECT COUNT(*) FROM likes l WHERE l.post_id = p.id AND l.user_id = ?) as user_liked
        FROM posts p
        JOIN users u ON p.user_id = u.id
        WHERE p.id = ?
        AND u.is_blocked = 0
        AND p.user_id NOT IN (
            SELECT blocked_id FROM blocked_users WHERE blocker_id = ?
        )
    ", [$currentUserId, $postId, $currentUserId]);
    
    if (!$post) {
        sendErrorResponse('Post not found', 404);
    }
    
    // Check if story is expired
    if ($post['is_story'] && isStoryExpired($post['story_expires_at'])) {
        sendErrorResponse('Story has expired', 404);
    }
    
    // Get comments for the post
    $comments = $db->fetchAll("
        SELECT 
            c.*,
            u.username,
            u.first_name,
            u.last_name,
            u.profile_image_url
        FROM comments c
        JOIN users u ON c.user_id = u.id
        WHERE c.post_id = ?
        AND u.is_blocked = 0
        ORDER BY c.created_at ASC
        LIMIT 50
    ", [$postId]);
    
    // Format user data
    $userData = [
        'id' => $post['user_id'],
        'username' => $post['username'],
        'first_name' => $post['first_name'],
        'last_name' => $post['last_name'],
        'profile_image_url' => $post['profile_image_url'],
        'country' => $post['country']
    ];
    
    // Format post data
    $formattedPost = formatPostData($post, $userData);
    $formattedPost['userLiked'] = (bool)$post['user_liked'];
    
    // Format comments
    $formattedComments = [];
    foreach ($comments as $comment) {
        $commentUser = [
            'id' => $comment['user_id'],
            'username' => $comment['username'],
            'first_name' => $comment['first_name'],
            'last_name' => $comment['last_name'],
            'profile_image_url' => $comment['profile_image_url']
        ];
        
        $formattedComments[] = [
            'id' => $comment['id'],
            'userId' => $comment['user_id'],
            'postId' => $comment['post_id'],
            'content' => $comment['content'],
            'createdAt' => $comment['created_at'],
            'user' => $commentUser
        ];
    }
    
    $formattedPost['comments'] = $formattedComments;
    
    sendJsonResponse($formattedPost);
    
} catch (Exception $e) {
    logError('Get post error: ' . $e->getMessage());
    sendErrorResponse($e->getMessage());
}
?>
